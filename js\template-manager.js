// Advanced Template Management System for ELASHRAFY CV
class TemplateManager {
    constructor() {
        this.templates = [];
        this.currentCategory = 'all';
        this.selectedTemplate = null;
        this.templateCategories = {
            modern: 'Modern & Contemporary',
            classic: 'Classic & Traditional', 
            creative: 'Creative & Artistic',
            executive: 'Executive & Leadership',
            academic: 'Academic & Research',
            technical: 'Technical & Engineering',
            medical: 'Medical & Healthcare',
            business: 'Business & Finance',
            legal: 'Legal & Law',
            education: 'Education & Teaching',
            sales: 'Sales & Marketing',
            design: 'Design & Creative'
        };
        
        this.init();
    }
    
    init() {
        this.loadTemplates();
        this.setupTemplateGallery();
        this.setupCategoryFilters();
    }
    
    async loadTemplates() {
        // Load template data - in a real app this would come from an API
        this.templates = await this.generateTemplateData();
        this.renderTemplateGrid();
    }
    
    async generateTemplateData() {
        // Generate 400+ professional templates across categories
        const templates = [];
        const categories = Object.keys(this.templateCategories);
        
        categories.forEach((category, categoryIndex) => {
            // Generate 50+ templates per category
            for (let i = 1; i <= 55; i++) {
                const templateId = `${category}_${i.toString().padStart(3, '0')}`;
                templates.push({
                    id: templateId,
                    name: `${this.templateCategories[category]} ${i}`,
                    category: category,
                    description: this.getTemplateDescription(category, i),
                    preview: this.generateTemplatePreview(category, i),
                    isPremium: i > 10, // First 10 templates in each category are free
                    rating: (4.0 + Math.random() * 1.0).toFixed(1),
                    downloads: Math.floor(Math.random() * 10000) + 1000,
                    tags: this.getTemplateTags(category),
                    colors: this.getTemplateColors(category),
                    layout: this.getTemplateLayout(category, i),
                    atsCompatible: true,
                    multiPage: i % 3 === 0, // Every 3rd template supports multiple pages
                    photoSupport: true,
                    customizable: true
                });
            }
        });
        
        return templates;
    }
    
    getTemplateDescription(category, index) {
        const descriptions = {
            modern: [
                'Clean and contemporary design with bold typography',
                'Minimalist layout with strategic use of color',
                'Professional design with modern visual elements',
                'Sleek and sophisticated professional template'
            ],
            classic: [
                'Traditional and timeless professional design',
                'Conservative layout perfect for corporate roles',
                'Elegant and formal presentation style',
                'Classic design with professional typography'
            ],
            creative: [
                'Innovative design with creative visual elements',
                'Artistic layout with unique color schemes',
                'Creative professional template with visual impact',
                'Dynamic design for creative professionals'
            ],
            executive: [
                'Premium design for senior leadership roles',
                'Executive-level presentation with luxury feel',
                'High-impact design for C-suite professionals',
                'Sophisticated layout for executive positions'
            ],
            academic: [
                'Scholarly design perfect for academic positions',
                'Research-focused layout with publication emphasis',
                'Academic template with clean organization',
                'Professional design for educational roles'
            ],
            technical: [
                'Technical professional design with clean lines',
                'Engineering-focused layout with skill emphasis',
                'Technology sector professional template',
                'Technical expertise showcase design'
            ]
        };
        
        const categoryDescriptions = descriptions[category] || descriptions.modern;
        return categoryDescriptions[index % categoryDescriptions.length];
    }
    
    getTemplateTags(category) {
        const tagMap = {
            modern: ['modern', 'clean', 'minimal', 'professional'],
            classic: ['traditional', 'formal', 'corporate', 'conservative'],
            creative: ['artistic', 'innovative', 'colorful', 'unique'],
            executive: ['premium', 'leadership', 'luxury', 'sophisticated'],
            academic: ['scholarly', 'research', 'education', 'formal'],
            technical: ['technical', 'engineering', 'skills-focused', 'clean']
        };
        
        return tagMap[category] || tagMap.modern;
    }
    
    getTemplateColors(category) {
        const colorSchemes = {
            modern: ['#2563eb', '#64748b', '#f59e0b'],
            classic: ['#1f2937', '#6b7280', '#dc2626'],
            creative: ['#7c3aed', '#06b6d4', '#f59e0b'],
            executive: ['#1e293b', '#8b5cf6', '#f59e0b'],
            academic: ['#374151', '#6366f1', '#059669'],
            technical: ['#0f172a', '#3b82f6', '#10b981']
        };
        
        return colorSchemes[category] || colorSchemes.modern;
    }
    
    getTemplateLayout(category, index) {
        const layouts = ['single-column', 'two-column', 'sidebar-left', 'sidebar-right', 'header-focus'];
        return layouts[index % layouts.length];
    }
    
    generateTemplatePreview(category, index) {
        // Generate SVG preview for template
        const colors = this.getTemplateColors(category);
        const layout = this.getTemplateLayout(category, index);
        
        return `data:image/svg+xml;base64,${btoa(this.createTemplateSVG(colors, layout, category))}`;
    }
    
    createTemplateSVG(colors, layout, category) {
        const [primary, secondary, accent] = colors;
        
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="280" height="200" fill="white" stroke="#e5e7eb" stroke-width="1"/>
            
            <!-- Header -->
            <rect x="20" y="20" width="240" height="40" fill="${primary}" rx="4"/>
            <rect x="30" y="30" width="60" height="8" fill="white" opacity="0.9" rx="2"/>
            <rect x="30" y="42" width="40" height="6" fill="white" opacity="0.7" rx="2"/>
            
            <!-- Photo placeholder -->
            <circle cx="220" cy="40" r="15" fill="white" opacity="0.8"/>
            
            <!-- Content sections -->
            <rect x="20" y="80" width="240" height="2" fill="${secondary}"/>
            <rect x="20" y="90" width="180" height="6" fill="${secondary}" opacity="0.6" rx="1"/>
            <rect x="20" y="100" width="200" height="4" fill="${secondary}" opacity="0.4" rx="1"/>
            <rect x="20" y="108" width="160" height="4" fill="${secondary}" opacity="0.4" rx="1"/>
            
            <rect x="20" y="125" width="240" height="2" fill="${accent}"/>
            <rect x="20" y="135" width="100" height="6" fill="${accent}" opacity="0.6" rx="1"/>
            <rect x="20" y="145" width="220" height="4" fill="${secondary}" opacity="0.4" rx="1"/>
            <rect x="20" y="153" width="180" height="4" fill="${secondary}" opacity="0.4" rx="1"/>
            
            <rect x="20" y="170" width="240" height="2" fill="${secondary}"/>
            <rect x="20" y="180" width="80" height="6" fill="${secondary}" opacity="0.6" rx="1"/>
            <rect x="110" y="180" width="80" height="6" fill="${secondary}" opacity="0.6" rx="1"/>
        </svg>`;
    }
    
    setupTemplateGallery() {
        const galleryBtn = document.getElementById('openTemplateGallery');
        const modal = document.getElementById('templateGalleryModal');
        const closeBtn = document.getElementById('closeTemplateGallery');
        const overlay = document.getElementById('modalOverlay');
        
        galleryBtn.addEventListener('click', () => {
            this.openTemplateGallery();
        });
        
        closeBtn.addEventListener('click', () => {
            this.closeTemplateGallery();
        });
        
        overlay.addEventListener('click', () => {
            this.closeTemplateGallery();
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                this.closeTemplateGallery();
            }
        });
    }
    
    setupCategoryFilters() {
        const categoryBtns = document.querySelectorAll('.category-btn');
        
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;
                this.filterByCategory(category);
                
                // Update active state
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
    }
    
    openTemplateGallery() {
        const modal = document.getElementById('templateGalleryModal');
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Load templates if not already loaded
        if (this.templates.length === 0) {
            this.loadTemplates();
        }
    }
    
    closeTemplateGallery() {
        const modal = document.getElementById('templateGalleryModal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    filterByCategory(category) {
        this.currentCategory = category;
        this.renderTemplateGrid();
    }
    
    renderTemplateGrid() {
        const grid = document.getElementById('templateGrid');
        const filteredTemplates = this.currentCategory === 'all' 
            ? this.templates 
            : this.templates.filter(t => t.category === this.currentCategory);
        
        grid.innerHTML = filteredTemplates.map(template => this.createTemplateCard(template)).join('');
        
        // Add event listeners to template cards
        grid.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => {
                const templateId = card.dataset.templateId;
                this.selectTemplate(templateId);
            });
        });
    }
    
    createTemplateCard(template) {
        const premiumBadge = template.isPremium ? '<div class="premium-badge">Premium</div>' : '';
        
        return `
            <div class="template-card" data-template-id="${template.id}">
                ${premiumBadge}
                <div class="template-preview-img">
                    <img src="${template.preview}" alt="${template.name}" loading="lazy">
                    <div class="template-overlay">
                        <button class="template-select-btn" data-translate="select_template">
                            Select Template
                        </button>
                    </div>
                </div>
                <div class="template-info">
                    <h3 class="template-name">${template.name}</h3>
                    <p class="template-category">${this.templateCategories[template.category]}</p>
                    <p class="template-description">${template.description}</p>
                    <div class="template-stats">
                        <span class="template-rating">
                            <i class="fas fa-star"></i> ${template.rating}
                        </span>
                        <span class="template-downloads">
                            <i class="fas fa-download"></i> ${template.downloads.toLocaleString()}
                        </span>
                    </div>
                    <div class="template-tags">
                        ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    selectTemplate(templateId) {
        const template = this.templates.find(t => t.id === templateId);
        if (!template) return;
        
        this.selectedTemplate = template;
        
        // Apply template to current CV
        this.applyTemplate(template);
        
        // Close gallery
        this.closeTemplateGallery();
        
        // Show success notification
        this.showNotification(
            `Template "${template.name}" applied successfully!`,
            'success'
        );
        
        // Update theme selector
        const themeSelect = document.getElementById('themeSelect');
        themeSelect.value = template.category;
        
        // Trigger theme change
        if (window.cvApp) {
            cvApp.changeTheme(template.category);
        }
    }
    
    applyTemplate(template) {
        // Store template data
        if (window.cvBuilder) {
            cvBuilder.formData.template = {
                id: template.id,
                name: template.name,
                category: template.category,
                colors: template.colors,
                layout: template.layout,
                settings: template
            };
            
            cvBuilder.saveToLocalStorage();
        }
        
        // Apply template styles
        this.applyTemplateStyles(template);
        
        // Update preview
        if (window.cvApp) {
            cvApp.updatePreview();
        }
    }
    
    applyTemplateStyles(template) {
        const cvPreview = document.getElementById('cvPreview');
        const [primary, secondary, accent] = template.colors;
        
        // Apply custom CSS variables for this template
        cvPreview.style.setProperty('--template-primary', primary);
        cvPreview.style.setProperty('--template-secondary', secondary);
        cvPreview.style.setProperty('--template-accent', accent);
        
        // Add template-specific classes
        cvPreview.className = `cv-preview theme-${template.category} layout-${template.layout}`;
        
        // Apply layout-specific styles
        this.applyLayoutStyles(template.layout);
    }
    
    applyLayoutStyles(layout) {
        const cvContent = document.querySelector('.cv-content');
        if (!cvContent) return;
        
        // Remove existing layout classes
        cvContent.classList.remove('single-column', 'two-column', 'sidebar-left', 'sidebar-right', 'header-focus');
        
        // Add new layout class
        cvContent.classList.add(layout);
    }
    
    showNotification(message, type = 'info') {
        if (window.cvApp) {
            cvApp.showNotification(message, type);
        }
    }
    
    // Get current template data
    getCurrentTemplate() {
        return this.selectedTemplate;
    }
    
    // Load template from saved data
    loadTemplate(templateData) {
        if (templateData && templateData.id) {
            const template = this.templates.find(t => t.id === templateData.id);
            if (template) {
                this.selectedTemplate = template;
                this.applyTemplateStyles(template);
            }
        }
    }
    
    // Search templates
    searchTemplates(query) {
        const searchTerm = query.toLowerCase();
        return this.templates.filter(template => 
            template.name.toLowerCase().includes(searchTerm) ||
            template.description.toLowerCase().includes(searchTerm) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            this.templateCategories[template.category].toLowerCase().includes(searchTerm)
        );
    }
    
    // Get templates by category
    getTemplatesByCategory(category) {
        return this.templates.filter(t => t.category === category);
    }
    
    // Get premium templates
    getPremiumTemplates() {
        return this.templates.filter(t => t.isPremium);
    }
    
    // Get free templates
    getFreeTemplates() {
        return this.templates.filter(t => !t.isPremium);
    }
}

// Initialize template manager
const templateManager = new TemplateManager();

// Export for global access
window.templateManager = templateManager;
