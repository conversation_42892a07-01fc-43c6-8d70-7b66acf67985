// Advanced Template Management System for ELASHRAFY CV
class TemplateManager {
    constructor() {
        this.templates = [];
        this.currentCategory = 'all';
        this.selectedTemplate = null;
        this.templateCategories = {
            modern: 'Modern & Contemporary',
            classic: 'Classic & Traditional', 
            creative: 'Creative & Artistic',
            executive: 'Executive & Leadership',
            academic: 'Academic & Research',
            technical: 'Technical & Engineering',
            medical: 'Medical & Healthcare',
            business: 'Business & Finance',
            legal: 'Legal & Law',
            education: 'Education & Teaching',
            sales: 'Sales & Marketing',
            design: 'Design & Creative'
        };
        
        this.init();
    }
    
    init() {
        this.loadTemplates();
        this.setupTemplateGallery();
        this.setupCategoryFilters();
    }
    
    async loadTemplates() {
        // Load template data - in a real app this would come from an API
        this.templates = await this.generateTemplateData();
        this.renderTemplateGrid();
    }
    
    async generateTemplateData() {
        // Generate 400+ professional templates across categories
        const templates = [];
        const categories = Object.keys(this.templateCategories);
        
        categories.forEach((category, categoryIndex) => {
            // Generate 50+ templates per category
            for (let i = 1; i <= 55; i++) {
                const templateId = `${category}_${i.toString().padStart(3, '0')}`;
                templates.push({
                    id: templateId,
                    name: `${this.templateCategories[category]} ${i}`,
                    category: category,
                    description: this.getTemplateDescription(category, i),
                    preview: this.generateTemplatePreview(category, i),
                    isPremium: i > 10, // First 10 templates in each category are free
                    rating: (4.0 + Math.random() * 1.0).toFixed(1),
                    downloads: Math.floor(Math.random() * 10000) + 1000,
                    tags: this.getTemplateTags(category),
                    colors: this.getTemplateColors(category),
                    layout: this.getTemplateLayout(category, i),
                    atsCompatible: true,
                    multiPage: i % 3 === 0, // Every 3rd template supports multiple pages
                    photoSupport: true,
                    customizable: true
                });
            }
        });
        
        return templates;
    }
    
    getTemplateDescription(category, index) {
        const descriptions = {
            modern: [
                'Clean and contemporary design with bold typography',
                'Minimalist layout with strategic use of color',
                'Professional design with modern visual elements',
                'Sleek and sophisticated professional template'
            ],
            classic: [
                'Traditional and timeless professional design',
                'Conservative layout perfect for corporate roles',
                'Elegant and formal presentation style',
                'Classic design with professional typography'
            ],
            creative: [
                'Innovative design with creative visual elements',
                'Artistic layout with unique color schemes',
                'Creative professional template with visual impact',
                'Dynamic design for creative professionals'
            ],
            executive: [
                'Premium design for senior leadership roles',
                'Executive-level presentation with luxury feel',
                'High-impact design for C-suite professionals',
                'Sophisticated layout for executive positions'
            ],
            academic: [
                'Scholarly design perfect for academic positions',
                'Research-focused layout with publication emphasis',
                'Academic template with clean organization',
                'Professional design for educational roles'
            ],
            technical: [
                'Technical professional design with clean lines',
                'Engineering-focused layout with skill emphasis',
                'Technology sector professional template',
                'Technical expertise showcase design'
            ]
        };
        
        const categoryDescriptions = descriptions[category] || descriptions.modern;
        return categoryDescriptions[index % categoryDescriptions.length];
    }
    
    getTemplateTags(category) {
        const tagMap = {
            modern: ['modern', 'clean', 'minimal', 'professional'],
            classic: ['traditional', 'formal', 'corporate', 'conservative'],
            creative: ['artistic', 'innovative', 'colorful', 'unique'],
            executive: ['premium', 'leadership', 'luxury', 'sophisticated'],
            academic: ['scholarly', 'research', 'education', 'formal'],
            technical: ['technical', 'engineering', 'skills-focused', 'clean']
        };
        
        return tagMap[category] || tagMap.modern;
    }
    
    getTemplateColors(category) {
        const colorSchemes = {
            modern: ['#2563eb', '#64748b', '#f59e0b'],
            classic: ['#1f2937', '#6b7280', '#dc2626'],
            creative: ['#7c3aed', '#06b6d4', '#f59e0b'],
            executive: ['#1e293b', '#8b5cf6', '#f59e0b'],
            academic: ['#374151', '#6366f1', '#059669'],
            technical: ['#0f172a', '#3b82f6', '#10b981']
        };
        
        return colorSchemes[category] || colorSchemes.modern;
    }
    
    getTemplateLayout(category, index) {
        const layouts = ['single-column', 'two-column', 'sidebar-left', 'sidebar-right', 'header-focus'];
        return layouts[index % layouts.length];
    }
    
    generateTemplatePreview(category, index) {
        // Generate SVG preview for template
        const colors = this.getTemplateColors(category);
        const layout = this.getTemplateLayout(category, index);
        
        return `data:image/svg+xml;base64,${btoa(this.createTemplateSVG(colors, layout, category))}`;
    }
    
    createTemplateSVG(colors, layout, category) {
        const [primary, secondary, accent] = colors;

        // إنشاء قوالب جميلة ومتنوعة حسب الفئة
        const templates = {
            modern: this.createModernTemplate(primary, secondary, accent),
            classic: this.createClassicTemplate(primary, secondary, accent),
            creative: this.createCreativeTemplate(primary, secondary, accent),
            executive: this.createExecutiveTemplate(primary, secondary, accent),
            academic: this.createAcademicTemplate(primary, secondary, accent),
            technical: this.createTechnicalTemplate(primary, secondary, accent),
            medical: this.createMedicalTemplate(primary, secondary, accent),
            business: this.createBusinessTemplate(primary, secondary, accent)
        };

        return templates[category] || templates.modern;
    }

    createModernTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="modernGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${accent};stop-opacity:1" />
                </linearGradient>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.1"/>
                </filter>
            </defs>

            <rect width="280" height="200" fill="white" rx="8"/>

            <!-- Modern Header with Gradient -->
            <rect x="0" y="0" width="280" height="60" fill="url(#modernGrad)" rx="8 8 0 0"/>

            <!-- Profile Photo -->
            <circle cx="50" cy="30" r="20" fill="white" opacity="0.9" filter="url(#shadow)"/>
            <circle cx="50" cy="30" r="15" fill="${accent}" opacity="0.3"/>

            <!-- Name and Title -->
            <rect x="80" y="20" width="120" height="8" fill="white" opacity="0.95" rx="4"/>
            <rect x="80" y="32" width="80" height="6" fill="white" opacity="0.8" rx="3"/>

            <!-- Contact Icons -->
            <circle cx="220" cy="25" r="3" fill="white" opacity="0.8"/>
            <circle cx="235" cy="25" r="3" fill="white" opacity="0.8"/>
            <circle cx="250" cy="25" r="3" fill="white" opacity="0.8"/>

            <!-- Modern Content Sections -->
            <rect x="20" y="80" width="240" height="1" fill="${primary}" opacity="0.3"/>
            <rect x="20" y="90" width="60" height="8" fill="${primary}" opacity="0.8" rx="4"/>
            <rect x="20" y="105" width="200" height="4" fill="${secondary}" opacity="0.6" rx="2"/>
            <rect x="20" y="113" width="180" height="4" fill="${secondary}" opacity="0.4" rx="2"/>
            <rect x="20" y="121" width="160" height="4" fill="${secondary}" opacity="0.4" rx="2"/>

            <!-- Skills Section -->
            <rect x="20" y="140" width="240" height="1" fill="${accent}" opacity="0.3"/>
            <rect x="20" y="150" width="50" height="8" fill="${accent}" opacity="0.8" rx="4"/>
            <rect x="20" y="165" width="40" height="6" fill="${accent}" opacity="0.6" rx="3"/>
            <rect x="70" y="165" width="35" height="6" fill="${accent}" opacity="0.6" rx="3"/>
            <rect x="115" y="165" width="45" height="6" fill="${accent}" opacity="0.6" rx="3"/>

            <!-- Decorative Elements -->
            <circle cx="260" cy="180" r="8" fill="${primary}" opacity="0.1"/>
            <circle cx="265" cy="185" r="5" fill="${accent}" opacity="0.1"/>
        </svg>`;
    }

    createCreativeTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="creativeGrad" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:0.3" />
                </radialGradient>
                <pattern id="dots" patternUnits="userSpaceOnUse" width="20" height="20">
                    <circle cx="10" cy="10" r="1" fill="${accent}" opacity="0.2"/>
                </pattern>
            </defs>

            <rect width="280" height="200" fill="url(#creativeGrad)" rx="12"/>
            <rect width="280" height="200" fill="url(#dots)" rx="12"/>

            <!-- Creative Header -->
            <path d="M0,0 Q140,40 280,0 L280,70 Q140,50 0,70 Z" fill="${primary}" opacity="0.9"/>

            <!-- Artistic Photo Frame -->
            <polygon points="40,25 65,20 70,45 45,50" fill="white" opacity="0.9"/>
            <circle cx="52" cy="35" r="12" fill="${accent}" opacity="0.7"/>

            <!-- Creative Text Layout -->
            <rect x="90" y="25" width="100" height="10" fill="white" opacity="0.95" rx="5"/>
            <rect x="90" y="40" width="70" height="6" fill="white" opacity="0.8" rx="3"/>

            <!-- Artistic Elements -->
            <path d="M200,20 Q220,30 240,20 Q250,35 260,25" stroke="white" stroke-width="2" fill="none" opacity="0.6"/>

            <!-- Content with Creative Layout -->
            <rect x="30" y="90" width="220" height="2" fill="${primary}" rx="1"/>
            <rect x="30" y="100" width="80" height="8" fill="${primary}" opacity="0.8" rx="4"/>

            <!-- Zigzag Content -->
            <rect x="30" y="115" width="180" height="4" fill="${secondary}" opacity="0.6" rx="2"/>
            <rect x="50" y="123" width="160" height="4" fill="${secondary}" opacity="0.5" rx="2"/>
            <rect x="30" y="131" width="170" height="4" fill="${secondary}" opacity="0.4" rx="2"/>

            <!-- Creative Skills -->
            <circle cx="40" cy="155" r="8" fill="${accent}" opacity="0.7"/>
            <circle cx="65" cy="155" r="6" fill="${accent}" opacity="0.6"/>
            <circle cx="85" cy="155" r="7" fill="${accent}" opacity="0.5"/>
            <circle cx="105" cy="155" r="5" fill="${accent}" opacity="0.6"/>

            <!-- Artistic Footer -->
            <path d="M0,180 Q70,170 140,180 Q210,190 280,180 L280,200 L0,200 Z" fill="${secondary}" opacity="0.3"/>
        </svg>`;
    }

    createExecutiveTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="execGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
                </linearGradient>
                <filter id="luxury" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="4" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
                </filter>
            </defs>

            <rect width="280" height="200" fill="#f8f9fa" rx="6"/>

            <!-- Executive Header -->
            <rect x="0" y="0" width="280" height="80" fill="url(#execGrad)" rx="6 6 0 0"/>

            <!-- Luxury Border -->
            <rect x="0" y="75" width="280" height="5" fill="${accent}"/>

            <!-- Executive Photo -->
            <rect x="200" y="15" width="50" height="50" fill="white" opacity="0.95" rx="4" filter="url(#luxury)"/>
            <rect x="205" y="20" width="40" height="40" fill="${accent}" opacity="0.3" rx="2"/>

            <!-- Executive Name -->
            <rect x="30" y="25" width="140" height="12" fill="white" opacity="0.98" rx="2"/>
            <rect x="30" y="42" width="90" height="8" fill="${accent}" opacity="0.9" rx="2"/>
            <rect x="30" y="55" width="120" height="6" fill="white" opacity="0.8" rx="2"/>

            <!-- Premium Content -->
            <rect x="30" y="100" width="220" height="3" fill="${primary}" rx="1"/>
            <rect x="30" y="110" width="100" height="10" fill="${primary}" opacity="0.9" rx="2"/>

            <!-- Executive Experience -->
            <rect x="30" y="130" width="200" height="5" fill="${secondary}" opacity="0.7" rx="2"/>
            <rect x="30" y="140" width="180" height="4" fill="${secondary}" opacity="0.5" rx="2"/>
            <rect x="30" y="148" width="190" height="4" fill="${secondary}" opacity="0.4" rx="2"/>

            <!-- Leadership Indicators -->
            <rect x="30" y="165" width="15" height="15" fill="${accent}" opacity="0.8" rx="2"/>
            <rect x="50" y="165" width="15" height="15" fill="${accent}" opacity="0.7" rx="2"/>
            <rect x="70" y="165" width="15" height="15" fill="${accent}" opacity="0.6" rx="2"/>

            <!-- Premium Footer -->
            <rect x="0" y="190" width="280" height="10" fill="${primary}" opacity="0.1" rx="0 0 6 6"/>
        </svg>`;
    }

    createClassicTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="280" height="200" fill="#fefefe" stroke="#e0e0e0" stroke-width="2" rx="4"/>

            <!-- Classic Header -->
            <rect x="0" y="0" width="280" height="50" fill="${primary}" rx="4 4 0 0"/>
            <rect x="0" y="45" width="280" height="5" fill="${accent}"/>

            <!-- Traditional Photo -->
            <rect x="30" y="15" width="40" height="40" fill="white" stroke="${secondary}" stroke-width="2" rx="2"/>
            <rect x="35" y="20" width="30" height="30" fill="${secondary}" opacity="0.3"/>

            <!-- Classic Name Layout -->
            <rect x="80" y="20" width="120" height="10" fill="white" rx="1"/>
            <rect x="80" y="35" width="80" height="6" fill="white" opacity="0.9" rx="1"/>

            <!-- Traditional Sections -->
            <rect x="20" y="70" width="240" height="2" fill="${primary}"/>
            <rect x="20" y="80" width="80" height="8" fill="${primary}" rx="1"/>
            <rect x="20" y="95" width="220" height="4" fill="${secondary}" opacity="0.7"/>
            <rect x="20" y="103" width="200" height="4" fill="${secondary}" opacity="0.5"/>
            <rect x="20" y="111" width="180" height="4" fill="${secondary}" opacity="0.4"/>

            <!-- Classic Experience -->
            <rect x="20" y="130" width="240" height="2" fill="${accent}"/>
            <rect x="20" y="140" width="70" height="8" fill="${accent}" rx="1"/>
            <rect x="20" y="155" width="190" height="4" fill="${secondary}" opacity="0.6"/>
            <rect x="20" y="163" width="170" height="4" fill="${secondary}" opacity="0.5"/>

            <!-- Traditional Footer -->
            <rect x="20" y="180" width="240" height="1" fill="${primary}" opacity="0.5"/>
        </svg>`;
    }

    createAcademicTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="280" height="200" fill="white" stroke="#d1d5db" stroke-width="1"/>

            <!-- Academic Header -->
            <rect x="20" y="20" width="240" height="40" fill="none" stroke="${primary}" stroke-width="2"/>
            <rect x="25" y="25" width="230" height="30" fill="${primary}" opacity="0.05"/>

            <!-- Academic Photo -->
            <circle cx="60" cy="40" r="15" fill="none" stroke="${secondary}" stroke-width="2"/>
            <circle cx="60" cy="40" r="10" fill="${secondary}" opacity="0.3"/>

            <!-- Academic Title -->
            <rect x="90" y="30" width="140" height="8" fill="${primary}" rx="1"/>
            <rect x="90" y="42" width="100" height="6" fill="${secondary}" opacity="0.8" rx="1"/>

            <!-- Publications Section -->
            <rect x="20" y="80" width="240" height="1" fill="${primary}"/>
            <rect x="20" y="90" width="90" height="8" fill="${primary}" opacity="0.9" rx="1"/>
            <rect x="20" y="105" width="200" height="3" fill="${secondary}" opacity="0.7"/>
            <rect x="20" y="112" width="180" height="3" fill="${secondary}" opacity="0.6"/>
            <rect x="20" y="119" width="190" height="3" fill="${secondary}" opacity="0.5"/>

            <!-- Research Areas -->
            <rect x="20" y="140" width="240" height="1" fill="${accent}"/>
            <rect x="20" y="150" width="80" height="8" fill="${accent}" opacity="0.9" rx="1"/>
            <rect x="20" y="165" width="60" height="5" fill="${accent}" opacity="0.7" rx="2"/>
            <rect x="90" y="165" width="70" height="5" fill="${accent}" opacity="0.7" rx="2"/>
            <rect x="170" y="165" width="50" height="5" fill="${accent}" opacity="0.7" rx="2"/>

            <!-- Academic Credentials -->
            <rect x="20" y="185" width="15" height="10" fill="${primary}" opacity="0.8"/>
            <rect x="40" y="185" width="15" height="10" fill="${primary}" opacity="0.7"/>
            <rect x="60" y="185" width="15" height="10" fill="${primary}" opacity="0.6"/>
        </svg>`;
    }

    createTechnicalTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="${secondary}" stroke-width="0.5" opacity="0.3"/>
                </pattern>
            </defs>

            <rect width="280" height="200" fill="#f8fafc"/>
            <rect width="280" height="200" fill="url(#grid)"/>

            <!-- Tech Header -->
            <rect x="0" y="0" width="280" height="50" fill="${primary}"/>
            <rect x="10" y="10" width="260" height="30" fill="none" stroke="${accent}" stroke-width="1"/>

            <!-- Code-style Photo -->
            <rect x="30" y="15" width="30" height="30" fill="${accent}" opacity="0.8"/>
            <rect x="32" y="17" width="26" height="26" fill="white" opacity="0.9"/>
            <rect x="35" y="20" width="20" height="20" fill="${primary}" opacity="0.3"/>

            <!-- Terminal-style Text -->
            <rect x="70" y="20" width="4" height="8" fill="${accent}"/>
            <rect x="78" y="20" width="80" height="8" fill="white" rx="1"/>
            <rect x="78" y="32" width="60" height="6" fill="${accent}" opacity="0.8" rx="1"/>

            <!-- Code Blocks -->
            <rect x="20" y="70" width="240" height="20" fill="${secondary}" opacity="0.1" stroke="${secondary}" stroke-width="1"/>
            <rect x="25" y="75" width="60" height="6" fill="${primary}" opacity="0.8" rx="1"/>
            <rect x="25" y="83" width="180" height="4" fill="${secondary}" opacity="0.6"/>

            <!-- Technical Skills -->
            <rect x="20" y="110" width="240" height="15" fill="${accent}" opacity="0.1" stroke="${accent}" stroke-width="1"/>
            <rect x="25" y="115" width="50" height="5" fill="${accent}" opacity="0.8" rx="2"/>
            <rect x="80" y="115" width="40" height="5" fill="${accent}" opacity="0.7" rx="2"/>
            <rect x="125" y="115" width="45" height="5" fill="${accent}" opacity="0.6" rx="2"/>

            <!-- Progress Bars -->
            <rect x="20" y="140" width="100" height="8" fill="${secondary}" opacity="0.3" rx="4"/>
            <rect x="20" y="140" width="80" height="8" fill="${primary}" rx="4"/>

            <rect x="20" y="155" width="100" height="8" fill="${secondary}" opacity="0.3" rx="4"/>
            <rect x="20" y="155" width="90" height="8" fill="${accent}" rx="4"/>

            <!-- Binary Footer -->
            <text x="20" y="185" font-family="monospace" font-size="8" fill="${secondary}" opacity="0.5">01001000 01100101 01101100 01101100 01101111</text>
        </svg>`;
    }

    createMedicalTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="280" height="200" fill="white" stroke="#e5e7eb" stroke-width="1" rx="6"/>

            <!-- Medical Header -->
            <rect x="0" y="0" width="280" height="60" fill="${primary}" rx="6 6 0 0"/>

            <!-- Medical Cross -->
            <rect x="240" y="15" width="6" height="30" fill="white" opacity="0.9"/>
            <rect x="228" y="27" width="30" height="6" fill="white" opacity="0.9"/>

            <!-- Professional Photo -->
            <circle cx="50" cy="30" r="18" fill="white" opacity="0.95"/>
            <circle cx="50" cy="30" r="14" fill="${accent}" opacity="0.3"/>

            <!-- Medical Title -->
            <rect x="80" y="20" width="120" height="10" fill="white" opacity="0.95" rx="2"/>
            <rect x="80" y="35" width="90" height="7" fill="white" opacity="0.8" rx="2"/>

            <!-- Specialization -->
            <rect x="20" y="80" width="240" height="2" fill="${primary}"/>
            <rect x="20" y="90" width="100" height="8" fill="${primary}" opacity="0.9" rx="2"/>
            <rect x="20" y="105" width="200" height="4" fill="${secondary}" opacity="0.7" rx="1"/>
            <rect x="20" y="113" width="180" height="4" fill="${secondary}" opacity="0.6" rx="1"/>

            <!-- Medical Experience -->
            <rect x="20" y="135" width="240" height="2" fill="${accent}"/>
            <rect x="20" y="145" width="80" height="8" fill="${accent}" opacity="0.9" rx="2"/>
            <rect x="20" y="160" width="170" height="4" fill="${secondary}" opacity="0.6" rx="1"/>
            <rect x="20" y="168" width="150" height="4" fill="${secondary}" opacity="0.5" rx="1"/>

            <!-- Medical Certifications -->
            <circle cx="30" cy="185" r="5" fill="${primary}" opacity="0.8"/>
            <circle cx="50" cy="185" r="5" fill="${accent}" opacity="0.8"/>
            <circle cx="70" cy="185" r="5" fill="${secondary}" opacity="0.8"/>

            <!-- Heartbeat Line -->
            <path d="M200,185 L210,185 L215,175 L220,195 L225,165 L230,185 L240,185"
                  stroke="${accent}" stroke-width="2" fill="none" opacity="0.6"/>
        </svg>`;
    }

    createBusinessTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="businessGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${accent};stop-opacity:0.8" />
                </linearGradient>
            </defs>

            <rect width="280" height="200" fill="#fafafa" rx="8"/>

            <!-- Business Header -->
            <rect x="0" y="0" width="280" height="70" fill="url(#businessGrad)" rx="8 8 0 0"/>

            <!-- Corporate Photo -->
            <rect x="200" y="15" width="50" height="50" fill="white" opacity="0.95" rx="6"/>
            <rect x="205" y="20" width="40" height="40" fill="${secondary}" opacity="0.3" rx="4"/>

            <!-- Business Title -->
            <rect x="30" y="25" width="140" height="12" fill="white" opacity="0.98" rx="3"/>
            <rect x="30" y="42" width="100" height="8" fill="white" opacity="0.85" rx="2"/>

            <!-- Corporate Sections -->
            <rect x="30" y="90" width="220" height="3" fill="${primary}" rx="1"/>
            <rect x="30" y="100" width="90" height="10" fill="${primary}" opacity="0.9" rx="2"/>

            <!-- Business Metrics -->
            <rect x="30" y="120" width="60" height="15" fill="${accent}" opacity="0.2" rx="3"/>
            <rect x="35" y="125" width="50" height="5" fill="${accent}" opacity="0.8" rx="2"/>

            <rect x="100" y="120" width="60" height="15" fill="${secondary}" opacity="0.2" rx="3"/>
            <rect x="105" y="125" width="50" height="5" fill="${secondary}" opacity="0.8" rx="2"/>

            <!-- Achievement Bars -->
            <rect x="30" y="150" width="200" height="6" fill="${secondary}" opacity="0.3" rx="3"/>
            <rect x="30" y="150" width="160" height="6" fill="${primary}" rx="3"/>

            <rect x="30" y="165" width="200" height="6" fill="${secondary}" opacity="0.3" rx="3"/>
            <rect x="30" y="165" width="140" height="6" fill="${accent}" rx="3"/>

            <!-- Business Icons -->
            <rect x="30" y="185" width="12" height="10" fill="${primary}" opacity="0.7" rx="1"/>
            <rect x="50" y="185" width="12" height="10" fill="${accent}" opacity="0.7" rx="1"/>
            <rect x="70" y="185" width="12" height="10" fill="${secondary}" opacity="0.7" rx="1"/>
        </svg>`;
    }

    setupTemplateGallery() {
        const galleryBtn = document.getElementById('openTemplateGallery');
        const modal = document.getElementById('templateGalleryModal');
        const closeBtn = document.getElementById('closeTemplateGallery');
        const overlay = document.getElementById('modalOverlay');
        
        galleryBtn.addEventListener('click', () => {
            this.openTemplateGallery();
        });
        
        closeBtn.addEventListener('click', () => {
            this.closeTemplateGallery();
        });
        
        overlay.addEventListener('click', () => {
            this.closeTemplateGallery();
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                this.closeTemplateGallery();
            }
        });
    }
    
    setupCategoryFilters() {
        const categoryBtns = document.querySelectorAll('.category-btn');
        
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const category = btn.dataset.category;
                this.filterByCategory(category);
                
                // Update active state
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
    }
    
    openTemplateGallery() {
        const modal = document.getElementById('templateGalleryModal');
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Load templates if not already loaded
        if (this.templates.length === 0) {
            this.loadTemplates();
        }
    }
    
    closeTemplateGallery() {
        const modal = document.getElementById('templateGalleryModal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    filterByCategory(category) {
        this.currentCategory = category;
        this.renderTemplateGrid();
    }
    
    renderTemplateGrid() {
        const grid = document.getElementById('templateGrid');
        const filteredTemplates = this.currentCategory === 'all' 
            ? this.templates 
            : this.templates.filter(t => t.category === this.currentCategory);
        
        grid.innerHTML = filteredTemplates.map(template => this.createTemplateCard(template)).join('');
        
        // Add event listeners to template cards
        grid.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => {
                const templateId = card.dataset.templateId;
                this.selectTemplate(templateId);
            });
        });
    }
    
    createTemplateCard(template) {
        const premiumBadge = template.isPremium ? '<div class="premium-badge">Premium</div>' : '';
        
        return `
            <div class="template-card" data-template-id="${template.id}">
                ${premiumBadge}
                <div class="template-preview-img">
                    <img src="${template.preview}" alt="${template.name}" loading="lazy">
                    <div class="template-overlay">
                        <button class="template-select-btn" data-translate="select_template">
                            Select Template
                        </button>
                    </div>
                </div>
                <div class="template-info">
                    <h3 class="template-name">${template.name}</h3>
                    <p class="template-category">${this.templateCategories[template.category]}</p>
                    <p class="template-description">${template.description}</p>
                    <div class="template-stats">
                        <span class="template-rating">
                            <i class="fas fa-star"></i> ${template.rating}
                        </span>
                        <span class="template-downloads">
                            <i class="fas fa-download"></i> ${template.downloads.toLocaleString()}
                        </span>
                    </div>
                    <div class="template-tags">
                        ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    selectTemplate(templateId) {
        const template = this.templates.find(t => t.id === templateId);
        if (!template) return;
        
        this.selectedTemplate = template;
        
        // Apply template to current CV
        this.applyTemplate(template);
        
        // Close gallery
        this.closeTemplateGallery();
        
        // Show success notification
        this.showNotification(
            `Template "${template.name}" applied successfully!`,
            'success'
        );
        
        // Update theme selector
        const themeSelect = document.getElementById('themeSelect');
        themeSelect.value = template.category;
        
        // Trigger theme change
        if (window.cvApp) {
            cvApp.changeTheme(template.category);
        }
    }
    
    applyTemplate(template) {
        // Store template data
        if (window.cvBuilder) {
            cvBuilder.formData.template = {
                id: template.id,
                name: template.name,
                category: template.category,
                colors: template.colors,
                layout: template.layout,
                settings: template
            };
            
            cvBuilder.saveToLocalStorage();
        }
        
        // Apply template styles
        this.applyTemplateStyles(template);
        
        // Update preview
        if (window.cvApp) {
            cvApp.updatePreview();
        }
    }
    
    applyTemplateStyles(template) {
        const cvPreview = document.getElementById('cvPreview');
        const [primary, secondary, accent] = template.colors;
        
        // Apply custom CSS variables for this template
        cvPreview.style.setProperty('--template-primary', primary);
        cvPreview.style.setProperty('--template-secondary', secondary);
        cvPreview.style.setProperty('--template-accent', accent);
        
        // Add template-specific classes
        cvPreview.className = `cv-preview theme-${template.category} layout-${template.layout}`;
        
        // Apply layout-specific styles
        this.applyLayoutStyles(template.layout);
    }
    
    applyLayoutStyles(layout) {
        const cvContent = document.querySelector('.cv-content');
        if (!cvContent) return;
        
        // Remove existing layout classes
        cvContent.classList.remove('single-column', 'two-column', 'sidebar-left', 'sidebar-right', 'header-focus');
        
        // Add new layout class
        cvContent.classList.add(layout);
    }
    
    showNotification(message, type = 'info') {
        if (window.cvApp) {
            cvApp.showNotification(message, type);
        }
    }
    
    // Get current template data
    getCurrentTemplate() {
        return this.selectedTemplate;
    }
    
    // Load template from saved data
    loadTemplate(templateData) {
        if (templateData && templateData.id) {
            const template = this.templates.find(t => t.id === templateData.id);
            if (template) {
                this.selectedTemplate = template;
                this.applyTemplateStyles(template);
            }
        }
    }
    
    // Search templates
    searchTemplates(query) {
        const searchTerm = query.toLowerCase();
        return this.templates.filter(template => 
            template.name.toLowerCase().includes(searchTerm) ||
            template.description.toLowerCase().includes(searchTerm) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            this.templateCategories[template.category].toLowerCase().includes(searchTerm)
        );
    }
    
    // Get templates by category
    getTemplatesByCategory(category) {
        return this.templates.filter(t => t.category === category);
    }
    
    // Get premium templates
    getPremiumTemplates() {
        return this.templates.filter(t => t.isPremium);
    }
    
    // Get free templates
    getFreeTemplates() {
        return this.templates.filter(t => !t.isPremium);
    }
}

// Initialize template manager
const templateManager = new TemplateManager();

// Export for global access
window.templateManager = templateManager;
