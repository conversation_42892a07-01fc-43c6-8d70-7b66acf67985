// Main Application Controller
class CVApp {
    constructor() {
        this.currentTheme = 'modern';
        this.zoomLevel = 100;
        this.init();
    }
    
    init() {
        this.setupThemeSelector();
        this.setupZoomControls();
        this.setupPreviewGenerator();
        this.setupDemoLoader();
        this.loadSavedSettings();

        // Initialize with default content
        this.generateInitialPreview();

        // Listen for language changes
        document.addEventListener('languageChanged', () => {
            this.updatePreview();
        });
    }
    
    setupThemeSelector() {
        const themeSelect = document.getElementById('themeSelect');
        
        themeSelect.addEventListener('change', (e) => {
            this.changeTheme(e.target.value);
        });
    }
    
    setupZoomControls() {
        const zoomInBtn = document.getElementById('zoomIn');
        const zoomOutBtn = document.getElementById('zoomOut');
        
        zoomInBtn.addEventListener('click', () => this.zoomIn());
        zoomOutBtn.addEventListener('click', () => this.zoomOut());
    }
    
    setupPreviewGenerator() {
        window.previewGenerator = {
            generate: (formData) => {
                this.generatePreview(formData);
            }
        };
    }

    setupDemoLoader() {
        const demoBtn = document.getElementById('loadDemo');

        demoBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('data/sample-data.json');
                const demoData = await response.json();

                // Load demo data into CV builder
                if (window.cvBuilder) {
                    cvBuilder.formData = demoData;
                    cvBuilder.populateForm();
                    this.updatePreview();

                    // Show success notification
                    this.showNotification(
                        languageManager.getCurrentLanguage() === 'ar' ?
                        'تم تحميل البيانات التجريبية بنجاح!' :
                        'Demo data loaded successfully!',
                        'success'
                    );
                }
            } catch (error) {
                console.error('Error loading demo data:', error);
                this.showNotification(
                    languageManager.getCurrentLanguage() === 'ar' ?
                    'خطأ في تحميل البيانات التجريبية' :
                    'Error loading demo data',
                    'error'
                );
            }
        });
    }
    
    changeTheme(theme) {
        this.currentTheme = theme;
        const cvPreview = document.getElementById('cvPreview');
        
        // Remove existing theme classes
        cvPreview.className = cvPreview.className.replace(/theme-\w+/g, '');
        
        // Add new theme class
        cvPreview.classList.add(`theme-${theme}`);
        
        // Save theme preference
        localStorage.setItem('cv-builder-theme', theme);
    }
    
    zoomIn() {
        if (this.zoomLevel < 150) {
            this.zoomLevel += 10;
            this.updateZoom();
        }
    }
    
    zoomOut() {
        if (this.zoomLevel > 50) {
            this.zoomLevel -= 10;
            this.updateZoom();
        }
    }
    
    updateZoom() {
        const cvPreview = document.getElementById('cvPreview');
        const zoomLevel = document.getElementById('zoomLevel');
        
        cvPreview.style.transform = `scale(${this.zoomLevel / 100})`;
        zoomLevel.textContent = `${this.zoomLevel}%`;
        
        // Save zoom preference
        localStorage.setItem('cv-builder-zoom', this.zoomLevel);
    }
    
    generatePreview(formData) {
        const cvPreview = document.getElementById('cvPreview');
        const content = this.generateCVContent(formData);
        
        cvPreview.innerHTML = content;
        
        // Apply current theme
        this.changeTheme(this.currentTheme);
    }
    
    generateCVContent(formData) {
        const personal = formData.personal || {};
        const experience = formData.experience || [];
        const education = formData.education || [];
        const skills = formData.skills || [];
        const languages = formData.languages || [];
        
        return `
            <div class="cv-content">
                ${this.generateHeader(personal)}
                ${personal.summary ? this.generateSummary(personal.summary) : ''}
                ${experience.length > 0 ? this.generateExperience(experience) : ''}
                ${education.length > 0 ? this.generateEducation(education) : ''}
                ${skills.length > 0 ? this.generateSkills(skills) : ''}
                ${languages.length > 0 ? this.generateLanguages(languages) : ''}
            </div>
        `;
    }
    
    generateHeader(personal) {
        const contactItems = [];
        
        if (personal.email) {
            contactItems.push(`<div class="cv-contact-item"><i class="fas fa-envelope"></i><span>${personal.email}</span></div>`);
        }
        if (personal.phone) {
            contactItems.push(`<div class="cv-contact-item"><i class="fas fa-phone"></i><span>${personal.phone}</span></div>`);
        }
        if (personal.address) {
            contactItems.push(`<div class="cv-contact-item"><i class="fas fa-map-marker-alt"></i><span>${personal.address}</span></div>`);
        }
        if (personal.linkedin) {
            contactItems.push(`<div class="cv-contact-item"><i class="fab fa-linkedin"></i><span>${personal.linkedin}</span></div>`);
        }
        if (personal.website) {
            contactItems.push(`<div class="cv-contact-item"><i class="fas fa-globe"></i><span>${personal.website}</span></div>`);
        }
        
        return `
            <div class="cv-header">
                <h1 class="cv-name">${personal.fullName || languageManager.getTranslation('full_name')}</h1>
                <h2 class="cv-title">${personal.jobTitle || languageManager.getTranslation('job_title')}</h2>
                <div class="cv-contact">
                    ${contactItems.join('')}
                </div>
            </div>
        `;
    }
    
    generateSummary(summary) {
        return `
            <div class="cv-section">
                <h3 class="cv-section-title">
                    <i class="fas fa-user"></i>
                    <span>${languageManager.getTranslation('summary')}</span>
                </h3>
                <p class="cv-summary">${summary}</p>
            </div>
        `;
    }
    
    generateExperience(experience) {
        const experienceItems = experience.map(exp => {
            const startDate = exp.startDate ? languageManager.formatDate(exp.startDate) : '';
            const endDate = exp.current ? languageManager.getTranslation('current_position') : 
                           (exp.endDate ? languageManager.formatDate(exp.endDate) : '');
            const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : startDate || endDate;
            
            return `
                <div class="cv-experience-item">
                    <div class="cv-item-header">
                        <div>
                            <h4 class="cv-item-title">${exp.position || ''}</h4>
                            <p class="cv-item-company">${exp.company || ''}</p>
                        </div>
                        <span class="cv-item-date">${dateRange}</span>
                    </div>
                    ${exp.description ? `<p class="cv-item-description">${exp.description}</p>` : ''}
                </div>
            `;
        }).join('');
        
        return `
            <div class="cv-section">
                <h3 class="cv-section-title">
                    <i class="fas fa-briefcase"></i>
                    <span>${languageManager.getTranslation('experience')}</span>
                </h3>
                ${experienceItems}
            </div>
        `;
    }
    
    generateEducation(education) {
        const educationItems = education.map(edu => {
            const graduationDate = edu.graduationDate ? languageManager.formatDate(edu.graduationDate) : '';
            
            return `
                <div class="cv-education-item">
                    <div class="cv-item-header">
                        <div>
                            <h4 class="cv-item-title">${edu.degree || ''}</h4>
                            <p class="cv-item-institution">${edu.institution || ''}</p>
                        </div>
                        <span class="cv-item-date">${graduationDate}</span>
                    </div>
                    ${edu.gpa ? `<p class="cv-item-description">GPA: ${edu.gpa}</p>` : ''}
                    ${edu.description ? `<p class="cv-item-description">${edu.description}</p>` : ''}
                </div>
            `;
        }).join('');
        
        return `
            <div class="cv-section">
                <h3 class="cv-section-title">
                    <i class="fas fa-graduation-cap"></i>
                    <span>${languageManager.getTranslation('education_title')}</span>
                </h3>
                ${educationItems}
            </div>
        `;
    }
    
    generateSkills(skills) {
        const skillItems = skills.map(skill => `
            <div class="cv-skill-item">
                <span class="cv-skill-name">${skill.name}</span>
                <span class="cv-skill-level">${skill.level}</span>
            </div>
        `).join('');
        
        return `
            <div class="cv-section">
                <h3 class="cv-section-title">
                    <i class="fas fa-cogs"></i>
                    <span>${languageManager.getTranslation('skills_title')}</span>
                </h3>
                <div class="cv-skills-grid">
                    ${skillItems}
                </div>
            </div>
        `;
    }
    
    generateLanguages(languages) {
        const languageItems = languages.map(lang => `
            <div class="cv-language-item">
                <span class="cv-language-name">${lang.name}</span>
                <span class="cv-language-level">${languageManager.getTranslation(lang.level)}</span>
            </div>
        `).join('');
        
        return `
            <div class="cv-section">
                <h3 class="cv-section-title">
                    <i class="fas fa-language"></i>
                    <span>${languageManager.getTranslation('languages_title')}</span>
                </h3>
                <div class="cv-languages-grid">
                    ${languageItems}
                </div>
            </div>
        `;
    }
    
    generateInitialPreview() {
        // Generate preview with sample data or empty state
        const sampleData = {
            personal: {
                fullName: languageManager.getTranslation('full_name'),
                jobTitle: languageManager.getTranslation('job_title')
            },
            experience: [],
            education: [],
            skills: [],
            languages: []
        };
        
        this.generatePreview(sampleData);
    }
    
    updatePreview() {
        // Update preview with current form data
        if (window.cvBuilder) {
            const formData = cvBuilder.getFormData();
            this.generatePreview(formData);
        }
    }
    
    loadSavedSettings() {
        // Load saved theme
        const savedTheme = localStorage.getItem('cv-builder-theme');
        if (savedTheme) {
            this.currentTheme = savedTheme;
            document.getElementById('themeSelect').value = savedTheme;
        }
        
        // Load saved zoom level
        const savedZoom = localStorage.getItem('cv-builder-zoom');
        if (savedZoom) {
            this.zoomLevel = parseInt(savedZoom);
            this.updateZoom();
        }
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    formatText(text) {
        if (!text) return '';
        
        // Convert line breaks to HTML
        return text.replace(/\n/g, '<br>');
    }
    
    // Export current CV data
    exportData() {
        const formData = cvBuilder.getFormData();
        const dataStr = JSON.stringify(formData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'cv-data.json';
        link.click();
    }
    
    // Import CV data
    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                cvBuilder.formData = data;
                cvBuilder.populateForm();
                this.updatePreview();
            } catch (error) {
                console.error('Error importing data:', error);
                alert('Error importing data. Please check the file format.');
            }
        };
        reader.readAsText(file);
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: this.getNotificationColor(type),
            color: 'white',
            padding: '16px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: '10000',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '14px',
            fontWeight: '500',
            maxWidth: '400px',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease-in-out'
        });

        // Add to document
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    getNotificationColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvApp = new CVApp();
});

// Handle responsive behavior
window.addEventListener('resize', () => {
    // Adjust layout for mobile devices
    const isMobile = window.innerWidth <= 768;
    const appLayout = document.querySelector('.app-layout');
    
    if (isMobile) {
        appLayout.style.gridTemplateColumns = '1fr';
    } else {
        appLayout.style.gridTemplateColumns = '400px 1fr';
    }
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + S to save (export PDF)
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        pdfExporter.exportToPDF();
    }
    
    // Ctrl/Cmd + P to print
    if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        e.preventDefault();
        pdfExporter.printToPDF();
    }
});

// Service Worker registration for offline functionality (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Export for global access
window.CVApp = CVApp;
