/* CV Themes */

/* Modern Theme (Default) */
.cv-preview.theme-modern {
    --theme-primary: #2563eb;
    --theme-secondary: #64748b;
    --theme-accent: #f59e0b;
    --theme-text: #1e293b;
    --theme-text-light: #64748b;
    --theme-bg: #ffffff;
    --theme-bg-light: #f8fafc;
}

.cv-preview.theme-modern .cv-header {
    background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
    color: white;
    padding: var(--spacing-8);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    border-radius: 0;
    border-bottom: none;
}

.cv-preview.theme-modern .cv-name {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cv-preview.theme-modern .cv-title {
    color: rgba(255, 255, 255, 0.9);
}

.cv-preview.theme-modern .cv-contact {
    color: rgba(255, 255, 255, 0.8);
}

.cv-preview.theme-modern .cv-contact-item i {
    color: white;
}

.cv-preview.theme-modern .cv-section-title {
    color: var(--theme-primary);
    border-bottom-color: var(--theme-primary);
    font-weight: 700;
}

/* Classic Theme */
.cv-preview.theme-classic {
    --theme-primary: #1f2937;
    --theme-secondary: #6b7280;
    --theme-accent: #dc2626;
    --theme-text: #1f2937;
    --theme-text-light: #6b7280;
    --theme-bg: #ffffff;
    --theme-bg-light: #f9fafb;
}

.cv-preview.theme-classic .cv-header {
    border-bottom: 3px solid var(--theme-accent);
    background: var(--theme-bg-light);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    padding: var(--spacing-8);
}

.cv-preview.theme-classic .cv-name {
    color: var(--theme-primary);
    font-family: 'Times New Roman', serif;
}

.cv-preview.theme-classic .cv-title {
    color: var(--theme-accent);
    font-style: italic;
}

.cv-preview.theme-classic .cv-section-title {
    color: var(--theme-primary);
    border-bottom: 2px solid var(--theme-accent);
    font-family: 'Times New Roman', serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cv-preview.theme-classic .cv-contact-item i {
    color: var(--theme-accent);
}

.cv-preview.theme-classic .cv-item-company,
.cv-preview.theme-classic .cv-item-institution {
    color: var(--theme-accent);
    font-style: italic;
}

/* Creative Theme */
.cv-preview.theme-creative {
    --theme-primary: #7c3aed;
    --theme-secondary: #a78bfa;
    --theme-accent: #06b6d4;
    --theme-text: #1e293b;
    --theme-text-light: #64748b;
    --theme-bg: #ffffff;
    --theme-bg-light: #faf5ff;
}

.cv-preview.theme-creative {
    background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
}

.cv-preview.theme-creative .cv-header {
    background: var(--theme-bg-light);
    border: none;
    border-left: 8px solid var(--theme-primary);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    padding: var(--spacing-8);
    position: relative;
}

.cv-preview.theme-creative .cv-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    clip-path: polygon(100% 0, 0 0, 100% 100%);
}

.cv-preview.theme-creative .cv-name {
    color: var(--theme-primary);
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cv-preview.theme-creative .cv-title {
    color: var(--theme-secondary);
    font-weight: 600;
}

.cv-preview.theme-creative .cv-section-title {
    background: linear-gradient(90deg, var(--theme-primary), var(--theme-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-bottom: 2px solid transparent;
    border-image: linear-gradient(90deg, var(--theme-primary), var(--theme-accent)) 1;
    position: relative;
}

.cv-preview.theme-creative .cv-section-title::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--theme-accent);
    border-radius: 50%;
}

.cv-preview.theme-creative .cv-contact-item i {
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cv-preview.theme-creative .cv-skill-level {
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    color: white;
}

/* Minimal Theme */
.cv-preview.theme-minimal {
    --theme-primary: #000000;
    --theme-secondary: #666666;
    --theme-accent: #000000;
    --theme-text: #000000;
    --theme-text-light: #666666;
    --theme-bg: #ffffff;
    --theme-bg-light: #fafafa;
}

.cv-preview.theme-minimal {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    border: 1px solid #e5e5e5;
}

.cv-preview.theme-minimal .cv-header {
    border-bottom: 1px solid #e5e5e5;
    background: white;
    text-align: left;
}

.cv-preview.theme-minimal .cv-name {
    color: var(--theme-primary);
    font-weight: 300;
    font-size: 2.5rem;
    letter-spacing: -1px;
}

.cv-preview.theme-minimal .cv-title {
    color: var(--theme-secondary);
    font-weight: 400;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.cv-preview.theme-minimal .cv-contact {
    justify-content: flex-start;
    margin-top: var(--spacing-4);
}

.cv-preview.theme-minimal .cv-contact-item {
    font-size: 0.875rem;
    color: var(--theme-secondary);
}

.cv-preview.theme-minimal .cv-contact-item i {
    display: none;
}

.cv-preview.theme-minimal .cv-section-title {
    color: var(--theme-primary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: var(--spacing-6);
}

.cv-preview.theme-minimal .cv-section-title i {
    display: none;
}

.cv-preview.theme-minimal .cv-item-title {
    font-weight: 600;
    font-size: 1rem;
}

.cv-preview.theme-minimal .cv-item-company,
.cv-preview.theme-minimal .cv-item-institution {
    color: var(--theme-secondary);
    font-weight: 400;
}

.cv-preview.theme-minimal .cv-item-date {
    color: var(--theme-secondary);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cv-preview.theme-minimal .cv-skill-level {
    background: var(--theme-primary);
    color: white;
    font-size: 0.625rem;
}

.cv-preview.theme-minimal .cv-language-item {
    background: transparent;
    border: 1px solid #e5e5e5;
}

/* RTL Theme Adjustments */
[dir="rtl"] .cv-preview.theme-creative .cv-header {
    border-left: none;
    border-right: 8px solid var(--theme-primary);
}

[dir="rtl"] .cv-preview.theme-creative .cv-section-title::before {
    left: auto;
    right: -20px;
}

[dir="rtl"] .cv-preview.theme-minimal .cv-header {
    text-align: right;
}

[dir="rtl"] .cv-preview.theme-minimal .cv-contact {
    justify-content: flex-end;
}

/* Print Styles for All Themes */
@media print {
    .cv-preview {
        box-shadow: none !important;
        border-radius: 0 !important;
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
    }
    
    .cv-preview.theme-modern .cv-header {
        background: var(--theme-primary) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .cv-preview.theme-creative {
        background: white !important;
    }
    
    .cv-preview.theme-creative .cv-header::before {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Theme-specific animations */
.cv-preview.theme-creative .cv-section {
    animation: slideInFromLeft 0.6s ease-out;
    animation-fill-mode: both;
}

.cv-preview.theme-creative .cv-section:nth-child(2) { animation-delay: 0.1s; }
.cv-preview.theme-creative .cv-section:nth-child(3) { animation-delay: 0.2s; }
.cv-preview.theme-creative .cv-section:nth-child(4) { animation-delay: 0.3s; }
.cv-preview.theme-creative .cv-section:nth-child(5) { animation-delay: 0.4s; }

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Theme transition effects */
.cv-preview {
    transition: all 0.3s ease-in-out;
}

.cv-preview * {
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}
