// Language Support and Internationalization
class LanguageManager {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {
            en: {
                // App Interface
                app_title: 'ELASHRAFY CV',
                app_subtitle: 'Professional Resume Builder',
                build_cv: 'Build Your CV',
                preview: 'Preview',
                export_pdf: 'Export PDF',
                load_demo: 'Load Demo',
                generating_pdf: 'Generating PDF...',
                template_gallery: 'Template Gallery',
                
                // Themes
                theme_modern: 'Modern',
                theme_classic: 'Classic',
                theme_creative: 'Creative',
                theme_minimal: 'Minimal',
                theme_executive: 'Executive',
                theme_academic: 'Academic',
                theme_technical: 'Technical',
                theme_medical: 'Medical',
                
                // Navigation
                previous: 'Previous',
                next: 'Next',
                
                // Form Sections
                personal_info: 'Personal Information',
                work_experience: 'Work Experience',
                education: 'Education',
                skills: 'Skills',
                languages: 'Languages',
                
                // Personal Information Fields
                full_name: 'Full Name',
                job_title: 'Job Title',
                email: 'Email',
                phone: 'Phone',
                address: 'Address',
                linkedin: 'LinkedIn',
                website: 'Website',
                professional_summary: 'Professional Summary',

                // Photo Upload
                profile_photo: 'Profile Photo',
                add_photo: 'Add Photo',
                upload_photo: 'Upload Photo',
                remove_photo: 'Remove Photo',
                edit_photo: 'Edit Photo',
                photo_shape: 'Photo Shape',
                photo_position: 'Position',
                circle: 'Circle',
                square: 'Square',
                rounded: 'Rounded',
                left: 'Left',
                center: 'Center',
                right: 'Right',
                brightness: 'Brightness',
                contrast: 'Contrast',
                rotation: 'Rotation',
                reset: 'Reset',
                apply: 'Apply',
                
                // Experience Fields
                add_experience: 'Add Experience',
                position: 'Position',
                company: 'Company',
                start_date: 'Start Date',
                end_date: 'End Date',
                current_position: 'Current Position',
                description: 'Description',
                
                // Education Fields
                add_education: 'Add Education',
                degree: 'Degree',
                institution: 'Institution',
                graduation_date: 'Graduation Date',
                
                // Skills
                skill_name: 'Skill Name',
                skill_level: 'Level',
                beginner: 'Beginner',
                intermediate: 'Intermediate',
                advanced: 'Advanced',
                expert: 'Expert',
                
                // Languages
                add_language: 'Add Language',
                language_name: 'Language',
                proficiency: 'Proficiency',
                native: 'Native',
                fluent: 'Fluent',
                conversational: 'Conversational',
                basic: 'Basic',
                
                // CV Sections
                summary: 'Summary',
                experience: 'Experience',
                education_title: 'Education',
                skills_title: 'Skills',
                languages_title: 'Languages',
                
                // Placeholders
                enter_skill: 'Enter a skill...',
                enter_language: 'Enter a language...',
                summary_placeholder: 'Brief description of your professional background...',
                description_placeholder: 'Describe your responsibilities and achievements...',
                
                // Validation Messages
                required_field: 'This field is required',
                invalid_email: 'Please enter a valid email address',
                invalid_url: 'Please enter a valid URL',
                
                // Success Messages
                pdf_generated: 'PDF generated successfully!',
                data_saved: 'Your data has been saved',
                
                // Error Messages
                pdf_error: 'Error generating PDF. Please try again.',
                save_error: 'Error saving data. Please try again.',
                
                // Months
                january: 'January',
                february: 'February',
                march: 'March',
                april: 'April',
                may: 'May',
                june: 'June',
                july: 'July',
                august: 'August',
                september: 'September',
                october: 'October',
                november: 'November',
                december: 'December',

                // Template Gallery
                all_templates: 'All Templates',
                modern_templates: 'Modern',
                classic_templates: 'Classic',
                creative_templates: 'Creative',
                executive_templates: 'Executive',
                academic_templates: 'Academic',
                technical_templates: 'Technical',
                medical_templates: 'Medical',
                select_template: 'Select Template',
                template_preview: 'Template Preview'
            },
            ar: {
                // App Interface
                app_title: 'الأشرافي للسيرة الذاتية',
                app_subtitle: 'منشئ السيرة الذاتية المهنية',
                build_cv: 'إنشاء سيرتك الذاتية',
                preview: 'معاينة',
                export_pdf: 'تصدير PDF',
                load_demo: 'تحميل نموذج',
                generating_pdf: 'جاري إنشاء PDF...',
                template_gallery: 'معرض القوالب',
                
                // Themes
                theme_modern: 'عصري',
                theme_classic: 'كلاسيكي',
                theme_creative: 'إبداعي',
                theme_minimal: 'بسيط',
                
                // Navigation
                previous: 'السابق',
                next: 'التالي',
                
                // Form Sections
                personal_info: 'المعلومات الشخصية',
                work_experience: 'الخبرة العملية',
                education: 'التعليم',
                skills: 'المهارات',
                languages: 'اللغات',
                
                // Personal Information Fields
                full_name: 'الاسم الكامل',
                job_title: 'المسمى الوظيفي',
                email: 'البريد الإلكتروني',
                phone: 'رقم الهاتف',
                address: 'العنوان',
                linkedin: 'لينكد إن',
                website: 'الموقع الإلكتروني',
                professional_summary: 'الملخص المهني',
                
                // Experience Fields
                add_experience: 'إضافة خبرة',
                position: 'المنصب',
                company: 'الشركة',
                start_date: 'تاريخ البداية',
                end_date: 'تاريخ النهاية',
                current_position: 'المنصب الحالي',
                description: 'الوصف',
                
                // Education Fields
                add_education: 'إضافة تعليم',
                degree: 'الدرجة العلمية',
                institution: 'المؤسسة التعليمية',
                graduation_date: 'تاريخ التخرج',
                
                // Skills
                skill_name: 'اسم المهارة',
                skill_level: 'المستوى',
                beginner: 'مبتدئ',
                intermediate: 'متوسط',
                advanced: 'متقدم',
                expert: 'خبير',
                
                // Languages
                add_language: 'إضافة لغة',
                language_name: 'اللغة',
                proficiency: 'مستوى الإتقان',
                native: 'لغة أم',
                fluent: 'طلق',
                conversational: 'محادثة',
                basic: 'أساسي',
                
                // CV Sections
                summary: 'الملخص',
                experience: 'الخبرة',
                education_title: 'التعليم',
                skills_title: 'المهارات',
                languages_title: 'اللغات',
                
                // Placeholders
                enter_skill: 'أدخل مهارة...',
                enter_language: 'أدخل لغة...',
                summary_placeholder: 'وصف موجز لخلفيتك المهنية...',
                description_placeholder: 'اوصف مسؤولياتك وإنجازاتك...',
                
                // Validation Messages
                required_field: 'هذا الحقل مطلوب',
                invalid_email: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
                invalid_url: 'يرجى إدخال رابط صحيح',
                
                // Success Messages
                pdf_generated: 'تم إنشاء PDF بنجاح!',
                data_saved: 'تم حفظ بياناتك',
                
                // Error Messages
                pdf_error: 'خطأ في إنشاء PDF. يرجى المحاولة مرة أخرى.',
                save_error: 'خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى.',
                
                // Months
                january: 'يناير',
                february: 'فبراير',
                march: 'مارس',
                april: 'أبريل',
                may: 'مايو',
                june: 'يونيو',
                july: 'يوليو',
                august: 'أغسطس',
                september: 'سبتمبر',
                october: 'أكتوبر',
                november: 'نوفمبر',
                december: 'ديسمبر'
            }
        };
        
        this.init();
    }
    
    init() {
        // Set initial language based on browser preference or saved preference
        const savedLanguage = localStorage.getItem('cv-builder-language');
        const browserLanguage = navigator.language.startsWith('ar') ? 'ar' : 'en';
        this.currentLanguage = savedLanguage || browserLanguage;
        
        this.setupLanguageToggle();
        this.applyLanguage(this.currentLanguage);
    }
    
    setupLanguageToggle() {
        const langButtons = document.querySelectorAll('.lang-btn');
        
        langButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const lang = btn.dataset.lang;
                this.switchLanguage(lang);
            });
        });
    }
    
    switchLanguage(language) {
        if (this.currentLanguage === language) return;
        
        this.currentLanguage = language;
        localStorage.setItem('cv-builder-language', language);
        
        this.applyLanguage(language);
        this.updateActiveLanguageButton();
        
        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language }
        }));
    }
    
    applyLanguage(language) {
        // Update document direction and language
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
        
        // Update font family CSS variable
        document.documentElement.style.setProperty(
            '--font-family-primary',
            language === 'ar' ? 'var(--font-family-ar)' : 'var(--font-family-en)'
        );
        
        // Update all translatable elements
        this.updateTranslatableElements();
        
        // Update placeholders
        this.updatePlaceholders();
        
        // Update form validation messages
        this.updateValidationMessages();
    }
    
    updateTranslatableElements() {
        const elements = document.querySelectorAll('[data-translate]');
        
        elements.forEach(element => {
            const key = element.dataset.translate;
            const translation = this.getTranslation(key);
            
            if (translation) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.placeholder = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });
    }
    
    updatePlaceholders() {
        const placeholderMappings = {
            'summary': 'summary_placeholder',
            'skillInput': 'enter_skill',
            'description': 'description_placeholder'
        };
        
        Object.entries(placeholderMappings).forEach(([id, key]) => {
            const element = document.getElementById(id);
            if (element) {
                element.placeholder = this.getTranslation(key);
            }
        });
    }
    
    updateValidationMessages() {
        // This will be called by the form validation system
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                // Clear existing validation messages to refresh with new language
                input.setCustomValidity('');
            });
        });
    }
    
    updateActiveLanguageButton() {
        const langButtons = document.querySelectorAll('.lang-btn');
        
        langButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === this.currentLanguage);
        });
    }
    
    getTranslation(key) {
        return this.translations[this.currentLanguage]?.[key] || 
               this.translations['en'][key] || 
               key;
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    isRTL() {
        return this.currentLanguage === 'ar';
    }
    
    formatDate(date, format = 'month-year') {
        if (!date) return '';
        
        const dateObj = new Date(date);
        const month = dateObj.getMonth();
        const year = dateObj.getFullYear();
        
        const months = [
            'january', 'february', 'march', 'april', 'may', 'june',
            'july', 'august', 'september', 'october', 'november', 'december'
        ];
        
        const monthName = this.getTranslation(months[month]);
        
        if (format === 'month-year') {
            return this.isRTL() ? `${year} ${monthName}` : `${monthName} ${year}`;
        }
        
        return `${monthName} ${year}`;
    }
    
    // Validation message helpers
    getValidationMessage(type, fieldName = '') {
        const messages = {
            required: this.getTranslation('required_field'),
            email: this.getTranslation('invalid_email'),
            url: this.getTranslation('invalid_url')
        };
        
        return messages[type] || '';
    }
}

// Initialize language manager
const languageManager = new LanguageManager();

// Export for use in other modules
window.languageManager = languageManager;
