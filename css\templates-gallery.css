/* ELASHRAFY CV - Templates Gallery Styles */
/* أنماط معرض القوالب - الأشرافي للسيرة الذاتية */

/* Templates Grid */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-6);
    padding: var(--spacing-4) 0;
}

.templates-grid.view-list {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

/* Template Card */
.template-card {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.template-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-color);
}

.template-card.premium {
    border: 2px solid #f59e0b;
    background: linear-gradient(135deg, #fff 0%, #fffbeb 100%);
}

.template-card.premium:hover {
    border-color: #d97706;
    box-shadow: 0 25px 50px -12px rgba(245, 158, 11, 0.25);
}

/* Premium Badge */
.premium-badge {
    position: absolute;
    top: var(--spacing-3);
    right: var(--spacing-3);
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 10;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    box-shadow: var(--shadow-md);
}

.premium-badge i {
    font-size: var(--font-size-xs);
}

/* Template Preview */
.template-preview {
    position: relative;
    aspect-ratio: 3/4;
    overflow: hidden;
    background: var(--gray-100);
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.template-card:hover .template-preview img {
    transform: scale(1.05);
}

/* Template Overlay */
.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.8), rgba(139, 92, 246, 0.8));
    opacity: 0;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-actions {
    display: flex;
    gap: var(--spacing-3);
}

.template-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: var(--gray-700);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.template-btn:hover {
    background: white;
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.template-btn.select-btn:hover {
    background: var(--success-color);
    color: white;
}

.template-btn.favorite-btn:hover {
    background: var(--error-color);
    color: white;
}

/* Template Info */
.template-info {
    padding: var(--spacing-6);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-3);
}

.template-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    line-height: 1.3;
}

.template-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    flex-shrink: 0;
}

.stars {
    display: flex;
    gap: 1px;
}

.stars i {
    font-size: var(--font-size-sm);
    color: #fbbf24;
}

.rating-value {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-600);
}

.template-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-4);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Template Meta */
.template-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.template-tags {
    display: flex;
    gap: var(--spacing-2);
    flex-wrap: wrap;
}

.tag {
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.category-tag {
    background: var(--primary-color);
    color: white;
}

.style-tag {
    background: var(--secondary-color);
    color: white;
}

.template-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.stat i {
    font-size: var(--font-size-xs);
}

/* Template Features */
.template-features {
    display: flex;
    gap: var(--spacing-2);
    flex-wrap: wrap;
}

.feature-tag {
    padding: var(--spacing-1) var(--spacing-2);
    background: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* List View Styles */
.templates-grid.view-list .template-card {
    display: flex;
    align-items: center;
    padding: var(--spacing-4);
}

.templates-grid.view-list .template-preview {
    width: 200px;
    aspect-ratio: 3/4;
    flex-shrink: 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.templates-grid.view-list .template-info {
    flex: 1;
    padding: 0 var(--spacing-6);
}

.templates-grid.view-list .template-description {
    -webkit-line-clamp: 3;
}

/* Load More Button */
.load-more-container {
    text-align: center;
    padding: var(--spacing-8) 0;
}

.load-more-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4) var(--spacing-8);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    border-radius: var(--radius-xl);
    font-weight: 500;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-lg);
}

.load-more-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Empty States */
.loading-templates,
.templates-error,
.no-templates {
    text-align: center;
    padding: var(--spacing-16);
    color: var(--gray-600);
}

.loading-templates i,
.templates-error i,
.no-templates i {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-400);
}

.templates-error i {
    color: var(--error-color);
}

.loading-templates .loading-spinner i {
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading-templates h3,
.templates-error h3,
.no-templates h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-2);
}

.loading-templates p,
.templates-error p,
.no-templates p {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-6);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Template Preview Modal */
.template-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-modal);
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.template-preview-modal.active {
    opacity: 1;
    visibility: visible;
}

.preview-modal-content {
    background: white;
    border-radius: var(--radius-2xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
    display: flex;
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.preview-modal-image {
    flex: 2;
    max-width: 600px;
}

.preview-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-modal-info {
    flex: 1;
    padding: var(--spacing-8);
    display: flex;
    flex-direction: column;
    min-width: 350px;
}

.preview-modal-header {
    margin-bottom: var(--spacing-6);
}

.preview-modal-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.preview-modal-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
}

.preview-modal-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-6);
}

.preview-modal-features {
    margin-bottom: var(--spacing-6);
}

.preview-modal-features h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.preview-modal-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.preview-modal-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-1) 0;
    color: var(--gray-600);
}

.preview-modal-features li i {
    color: var(--success-color);
    font-size: var(--font-size-sm);
}

.preview-modal-actions {
    margin-top: auto;
    display: flex;
    gap: var(--spacing-3);
}

.preview-modal-btn {
    flex: 1;
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
}

.preview-modal-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.preview-modal-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.preview-modal-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.preview-modal-close {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.preview-modal-close:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-4);
    }
}

@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-3);
    }
    
    .template-info {
        padding: var(--spacing-4);
    }
    
    .template-actions {
        gap: var(--spacing-2);
    }
    
    .template-btn {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
    
    .templates-grid.view-list .template-card {
        flex-direction: column;
        text-align: center;
    }
    
    .templates-grid.view-list .template-preview {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }
    
    .templates-grid.view-list .template-info {
        padding: var(--spacing-4);
    }
    
    .preview-modal-content {
        flex-direction: column;
        max-width: 95vw;
        max-height: 95vh;
    }
    
    .preview-modal-image {
        max-width: none;
        max-height: 50vh;
    }
    
    .preview-modal-info {
        min-width: auto;
        padding: var(--spacing-4);
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .template-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .template-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .preview-modal-actions {
        flex-direction: column;
    }
}

/* RTL Support */
[dir="rtl"] .template-rating {
    flex-direction: row-reverse;
}

[dir="rtl"] .template-meta {
    flex-direction: row-reverse;
}

[dir="rtl"] .template-stats {
    flex-direction: row-reverse;
}

[dir="rtl"] .preview-modal-close {
    right: auto;
    left: var(--spacing-4);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .template-card {
        background: var(--gray-800);
        border-color: var(--gray-700);
        color: white;
    }
    
    .template-name {
        color: white;
    }
    
    .template-description {
        color: var(--gray-300);
    }
    
    .feature-tag {
        background: var(--gray-700);
        color: var(--gray-300);
    }
    
    .preview-modal-content {
        background: var(--gray-800);
        color: white;
    }
    
    .preview-modal-title {
        color: white;
    }
    
    .preview-modal-description {
        color: var(--gray-300);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .template-card {
        border-width: 2px;
    }
    
    .template-btn {
        border: 2px solid currentColor;
    }
    
    .tag {
        border: 1px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .template-card,
    .template-preview img,
    .template-overlay,
    .template-btn,
    .load-more-btn {
        transition: none;
    }
    
    .template-card:hover {
        transform: none;
    }
    
    .template-card:hover .template-preview img {
        transform: none;
    }
    
    .loading-spinner i {
        animation: none;
    }
    
    .preview-modal-content {
        animation: none;
    }
}
