// CV Builder Core Functionality
class CVBuilder {
    constructor() {
        this.currentSection = 0;
        this.sections = ['personal', 'experience', 'education', 'skills', 'languages'];
        this.formData = {
            personal: {},
            experience: [],
            education: [],
            skills: [],
            languages: []
        };
        
        this.init();
    }
    
    init() {
        this.setupFormNavigation();
        this.setupDynamicSections();
        this.setupFormValidation();
        this.setupAutoSave();
        this.loadSavedData();
        this.updateProgress();
        this.generatePreview();
        
        // Listen for language changes
        document.addEventListener('languageChanged', () => {
            this.generatePreview();
        });
    }
    
    setupFormNavigation() {
        const prevBtn = document.getElementById('prevSection');
        const nextBtn = document.getElementById('nextSection');
        
        prevBtn.addEventListener('click', () => this.previousSection());
        nextBtn.addEventListener('click', () => this.nextSection());
        
        this.updateNavigationButtons();
    }
    
    setupDynamicSections() {
        // Experience section
        document.getElementById('addExperience').addEventListener('click', () => {
            this.addExperienceItem();
        });
        
        // Education section
        document.getElementById('addEducation').addEventListener('click', () => {
            this.addEducationItem();
        });
        
        // Skills section
        document.getElementById('addSkill').addEventListener('click', () => {
            this.addSkill();
        });
        
        document.getElementById('skillInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addSkill();
            }
        });
        
        // Languages section
        document.getElementById('addLanguage').addEventListener('click', () => {
            this.addLanguageItem();
        });
    }
    
    setupFormValidation() {
        const form = document.getElementById('cvForm');
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => {
                this.saveFormData();
                this.generatePreview();
                this.updateProgress();
            });
        });
    }
    
    setupAutoSave() {
        // Auto-save every 30 seconds
        setInterval(() => {
            this.saveToLocalStorage();
        }, 30000);
        
        // Save on page unload
        window.addEventListener('beforeunload', () => {
            this.saveToLocalStorage();
        });
    }
    
    previousSection() {
        if (this.currentSection > 0) {
            this.currentSection--;
            this.showSection(this.currentSection);
            this.updateNavigationButtons();
        }
    }
    
    nextSection() {
        if (this.validateCurrentSection()) {
            if (this.currentSection < this.sections.length - 1) {
                this.currentSection++;
                this.showSection(this.currentSection);
                this.updateNavigationButtons();
            }
        }
    }
    
    showSection(index) {
        const sections = document.querySelectorAll('.form-section');
        
        sections.forEach((section, i) => {
            section.classList.toggle('active', i === index);
        });
        
        // Scroll to top of form
        document.querySelector('.cv-form').scrollTop = 0;
    }
    
    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevSection');
        const nextBtn = document.getElementById('nextSection');
        
        prevBtn.disabled = this.currentSection === 0;
        
        if (this.currentSection === this.sections.length - 1) {
            nextBtn.innerHTML = `
                <span data-translate="export_pdf">${languageManager.getTranslation('export_pdf')}</span>
                <i class="fas fa-download"></i>
            `;
            nextBtn.onclick = () => window.pdfExporter.exportToPDF();
        } else {
            nextBtn.innerHTML = `
                <span data-translate="next">${languageManager.getTranslation('next')}</span>
                <i class="fas fa-chevron-right"></i>
            `;
            nextBtn.onclick = () => this.nextSection();
        }
    }
    
    validateCurrentSection() {
        const currentSectionElement = document.querySelector('.form-section.active');
        const requiredFields = currentSectionElement.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';
        
        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = languageManager.getValidationMessage('required');
        }
        
        // Email validation
        if (field.type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = languageManager.getValidationMessage('email');
        }
        
        // URL validation
        if (field.type === 'url' && value && !this.isValidURL(value)) {
            isValid = false;
            message = languageManager.getValidationMessage('url');
        }
        
        // Update field styling
        field.classList.toggle('error', !isValid);
        
        // Show/hide error message
        this.showFieldError(field, message);
        
        return isValid;
    }
    
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    isValidURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
    
    showFieldError(field, message) {
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message if needed
        if (message) {
            const errorElement = document.createElement('span');
            errorElement.className = 'error-message';
            errorElement.textContent = message;
            errorElement.style.color = 'var(--error-color)';
            errorElement.style.fontSize = 'var(--font-size-sm)';
            errorElement.style.marginTop = 'var(--spacing-1)';
            field.parentNode.appendChild(errorElement);
        }
    }
    
    addExperienceItem() {
        const container = document.getElementById('experienceContainer');
        const index = container.children.length;
        
        const experienceHTML = `
            <div class="dynamic-item" data-index="${index}">
                <button type="button" class="remove-btn" onclick="cvBuilder.removeExperienceItem(${index})">
                    <i class="fas fa-times"></i>
                </button>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label data-translate="position">${languageManager.getTranslation('position')}</label>
                        <input type="text" name="experience[${index}][position]" required>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="company">${languageManager.getTranslation('company')}</label>
                        <input type="text" name="experience[${index}][company]" required>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="start_date">${languageManager.getTranslation('start_date')}</label>
                        <input type="month" name="experience[${index}][startDate]" required>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="end_date">${languageManager.getTranslation('end_date')}</label>
                        <input type="month" name="experience[${index}][endDate]">
                        <label class="checkbox-label">
                            <input type="checkbox" name="experience[${index}][current]" onchange="cvBuilder.toggleCurrentPosition(${index})">
                            <span data-translate="current_position">${languageManager.getTranslation('current_position')}</span>
                        </label>
                    </div>
                    
                    <div class="form-group full-width">
                        <label data-translate="description">${languageManager.getTranslation('description')}</label>
                        <textarea name="experience[${index}][description]" rows="3" placeholder="${languageManager.getTranslation('description_placeholder')}"></textarea>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', experienceHTML);
        this.setupNewItemEvents(container.lastElementChild);
    }
    
    removeExperienceItem(index) {
        const item = document.querySelector(`[data-index="${index}"]`);
        if (item) {
            item.remove();
            this.saveFormData();
            this.generatePreview();
        }
    }
    
    toggleCurrentPosition(index) {
        const endDateInput = document.querySelector(`input[name="experience[${index}][endDate]"]`);
        const currentCheckbox = document.querySelector(`input[name="experience[${index}][current]"]`);
        
        if (currentCheckbox.checked) {
            endDateInput.disabled = true;
            endDateInput.value = '';
        } else {
            endDateInput.disabled = false;
        }
        
        this.saveFormData();
        this.generatePreview();
    }
    
    addEducationItem() {
        const container = document.getElementById('educationContainer');
        const index = container.children.length;
        
        const educationHTML = `
            <div class="dynamic-item" data-index="${index}">
                <button type="button" class="remove-btn" onclick="cvBuilder.removeEducationItem(${index})">
                    <i class="fas fa-times"></i>
                </button>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label data-translate="degree">${languageManager.getTranslation('degree')}</label>
                        <input type="text" name="education[${index}][degree]" required>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="institution">${languageManager.getTranslation('institution')}</label>
                        <input type="text" name="education[${index}][institution]" required>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="graduation_date">${languageManager.getTranslation('graduation_date')}</label>
                        <input type="month" name="education[${index}][graduationDate]" required>
                    </div>
                    
                    <div class="form-group">
                        <label>GPA (Optional)</label>
                        <input type="text" name="education[${index}][gpa]">
                    </div>
                    
                    <div class="form-group full-width">
                        <label data-translate="description">${languageManager.getTranslation('description')}</label>
                        <textarea name="education[${index}][description]" rows="2"></textarea>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', educationHTML);
        this.setupNewItemEvents(container.lastElementChild);
    }
    
    removeEducationItem(index) {
        const item = document.querySelector(`[data-index="${index}"]`);
        if (item) {
            item.remove();
            this.saveFormData();
            this.generatePreview();
        }
    }
    
    addSkill() {
        const skillInput = document.getElementById('skillInput');
        const skillLevel = document.getElementById('skillLevel');
        const skillsContainer = document.getElementById('skillsContainer');
        
        const skillName = skillInput.value.trim();
        const level = skillLevel.value;
        
        if (!skillName) return;
        
        // Check if skill already exists
        const existingSkill = Array.from(skillsContainer.children).find(
            skill => skill.dataset.skill === skillName
        );
        
        if (existingSkill) {
            skillInput.value = '';
            return;
        }
        
        const skillHTML = `
            <div class="skill-tag" data-skill="${skillName}">
                <span class="skill-name">${skillName}</span>
                <span class="skill-level">${languageManager.getTranslation(level)}</span>
                <button type="button" class="remove-skill" onclick="cvBuilder.removeSkill('${skillName}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        skillsContainer.insertAdjacentHTML('beforeend', skillHTML);
        
        skillInput.value = '';
        skillLevel.value = 'intermediate';
        
        this.saveFormData();
        this.generatePreview();
    }
    
    removeSkill(skillName) {
        const skillElement = document.querySelector(`[data-skill="${skillName}"]`);
        if (skillElement) {
            skillElement.remove();
            this.saveFormData();
            this.generatePreview();
        }
    }
    
    addLanguageItem() {
        const container = document.getElementById('languagesContainer');
        const index = container.children.length;
        
        const languageHTML = `
            <div class="dynamic-item" data-index="${index}">
                <button type="button" class="remove-btn" onclick="cvBuilder.removeLanguageItem(${index})">
                    <i class="fas fa-times"></i>
                </button>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label data-translate="language_name">${languageManager.getTranslation('language_name')}</label>
                        <input type="text" name="languages[${index}][name]" required>
                    </div>
                    
                    <div class="form-group">
                        <label data-translate="proficiency">${languageManager.getTranslation('proficiency')}</label>
                        <select name="languages[${index}][level]" required>
                            <option value="native" data-translate="native">${languageManager.getTranslation('native')}</option>
                            <option value="fluent" data-translate="fluent">${languageManager.getTranslation('fluent')}</option>
                            <option value="conversational" data-translate="conversational">${languageManager.getTranslation('conversational')}</option>
                            <option value="basic" data-translate="basic">${languageManager.getTranslation('basic')}</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', languageHTML);
        this.setupNewItemEvents(container.lastElementChild);
    }
    
    removeLanguageItem(index) {
        const item = document.querySelector(`[data-index="${index}"]`);
        if (item) {
            item.remove();
            this.saveFormData();
            this.generatePreview();
        }
    }
    
    setupNewItemEvents(element) {
        const inputs = element.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.saveFormData();
                this.generatePreview();
                this.updateProgress();
            });
            input.addEventListener('blur', () => this.validateField(input));
        });
    }
    
    saveFormData() {
        const form = document.getElementById('cvForm');
        const formData = new FormData(form);
        
        // Reset form data
        this.formData = {
            personal: {},
            experience: [],
            education: [],
            skills: [],
            languages: []
        };
        
        // Collect personal information
        const personalFields = ['fullName', 'jobTitle', 'email', 'phone', 'address', 'linkedin', 'website', 'summary'];
        personalFields.forEach(field => {
            const value = formData.get(field);
            if (value) {
                this.formData.personal[field] = value;
            }
        });
        
        // Collect experience data
        const experienceItems = document.querySelectorAll('#experienceContainer .dynamic-item');
        experienceItems.forEach((item, index) => {
            const experience = {};
            const inputs = item.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                const name = input.name.split('[')[2].replace(']', '');
                if (input.type === 'checkbox') {
                    experience[name] = input.checked;
                } else if (input.value) {
                    experience[name] = input.value;
                }
            });
            if (Object.keys(experience).length > 0) {
                this.formData.experience.push(experience);
            }
        });
        
        // Collect education data
        const educationItems = document.querySelectorAll('#educationContainer .dynamic-item');
        educationItems.forEach((item, index) => {
            const education = {};
            const inputs = item.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                const name = input.name.split('[')[2].replace(']', '');
                if (input.value) {
                    education[name] = input.value;
                }
            });
            if (Object.keys(education).length > 0) {
                this.formData.education.push(education);
            }
        });
        
        // Collect skills data
        const skillTags = document.querySelectorAll('#skillsContainer .skill-tag');
        this.formData.skills = Array.from(skillTags).map(tag => ({
            name: tag.dataset.skill,
            level: tag.querySelector('.skill-level').textContent
        }));
        
        // Collect languages data
        const languageItems = document.querySelectorAll('#languagesContainer .dynamic-item');
        languageItems.forEach((item, index) => {
            const language = {};
            const inputs = item.querySelectorAll('input, select');
            inputs.forEach(input => {
                const name = input.name.split('[')[2].replace(']', '');
                if (input.value) {
                    language[name] = input.value;
                }
            });
            if (Object.keys(language).length > 0) {
                this.formData.languages.push(language);
            }
        });
    }
    
    updateProgress() {
        const totalFields = this.getTotalRequiredFields();
        const completedFields = this.getCompletedRequiredFields();
        const percentage = totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
        
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${percentage}% Complete`;
    }
    
    getTotalRequiredFields() {
        return document.querySelectorAll('#cvForm [required]').length;
    }
    
    getCompletedRequiredFields() {
        const requiredFields = document.querySelectorAll('#cvForm [required]');
        return Array.from(requiredFields).filter(field => field.value.trim() !== '').length;
    }
    
    generatePreview() {
        // This will be implemented in the preview generation
        if (window.previewGenerator) {
            window.previewGenerator.generate(this.formData);
        }
    }
    
    saveToLocalStorage() {
        localStorage.setItem('cv-builder-data', JSON.stringify(this.formData));
    }
    
    loadSavedData() {
        const savedData = localStorage.getItem('cv-builder-data');
        if (savedData) {
            try {
                this.formData = JSON.parse(savedData);
                this.populateForm();
            } catch (error) {
                console.error('Error loading saved data:', error);
            }
        }
    }
    
    populateForm() {
        // Populate personal information
        Object.entries(this.formData.personal || {}).forEach(([key, value]) => {
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = value;
            }
        });
        
        // Populate experience
        (this.formData.experience || []).forEach(() => {
            this.addExperienceItem();
        });
        
        // Populate education
        (this.formData.education || []).forEach(() => {
            this.addEducationItem();
        });
        
        // Populate skills
        (this.formData.skills || []).forEach(skill => {
            document.getElementById('skillInput').value = skill.name;
            // Set level based on skill.level
            this.addSkill();
        });
        
        // Populate languages
        (this.formData.languages || []).forEach(() => {
            this.addLanguageItem();
        });
        
        // Re-populate dynamic fields after creation
        setTimeout(() => {
            this.populateDynamicFields();
        }, 100);
    }
    
    populateDynamicFields() {
        // Populate experience fields
        (this.formData.experience || []).forEach((exp, index) => {
            Object.entries(exp).forEach(([key, value]) => {
                const input = document.querySelector(`[name="experience[${index}][${key}]"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = value;
                        if (key === 'current' && value) {
                            this.toggleCurrentPosition(index);
                        }
                    } else {
                        input.value = value;
                    }
                }
            });
        });
        
        // Populate education fields
        (this.formData.education || []).forEach((edu, index) => {
            Object.entries(edu).forEach(([key, value]) => {
                const input = document.querySelector(`[name="education[${index}][${key}]"]`);
                if (input) {
                    input.value = value;
                }
            });
        });
        
        // Populate language fields
        (this.formData.languages || []).forEach((lang, index) => {
            Object.entries(lang).forEach(([key, value]) => {
                const input = document.querySelector(`[name="languages[${index}][${key}]"]`);
                if (input) {
                    input.value = value;
                }
            });
        });
        
        this.generatePreview();
        this.updateProgress();
    }
    
    getFormData() {
        this.saveFormData();
        return this.formData;
    }
}

// Initialize CV Builder
const cvBuilder = new CVBuilder();

// Export for global access
window.cvBuilder = cvBuilder;
