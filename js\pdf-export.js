// PDF Export Functionality
class PDFExporter {
    constructor() {
        this.isExporting = false;
        this.init();
    }
    
    init() {
        // Setup export button
        document.getElementById('exportPDF').addEventListener('click', () => {
            this.exportToPDF();
        });
    }
    
    async exportToPDF() {
        if (this.isExporting) return;
        
        try {
            this.isExporting = true;
            this.showLoadingOverlay();
            
            // Get the CV preview element
            const cvPreview = document.getElementById('cvPreview');
            if (!cvPreview) {
                throw new Error('CV preview not found');
            }
            
            // Prepare the element for PDF generation
            const originalTransform = cvPreview.style.transform;
            const originalWidth = cvPreview.style.width;
            const originalMaxWidth = cvPreview.style.maxWidth;
            
            // Reset transform and set optimal size for PDF
            cvPreview.style.transform = 'none';
            cvPreview.style.width = '210mm';
            cvPreview.style.maxWidth = 'none';
            
            // Wait a moment for styles to apply
            await this.delay(100);
            
            // Generate canvas from HTML
            const canvas = await html2canvas(cvPreview, {
                scale: 2, // Higher resolution
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 794, // A4 width in pixels at 96 DPI
                height: 1123, // A4 height in pixels at 96 DPI
                scrollX: 0,
                scrollY: 0,
                windowWidth: 794,
                windowHeight: 1123
            });
            
            // Restore original styles
            cvPreview.style.transform = originalTransform;
            cvPreview.style.width = originalWidth;
            cvPreview.style.maxWidth = originalMaxWidth;
            
            // Create PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });
            
            // Calculate dimensions
            const imgWidth = 210; // A4 width in mm
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            
            // Add image to PDF
            const imgData = canvas.toDataURL('image/png');
            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
            
            // If content is longer than one page, handle pagination
            if (imgHeight > 297) { // A4 height in mm
                let position = 297;
                while (position < imgHeight) {
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, -position, imgWidth, imgHeight);
                    position += 297;
                }
            }
            
            // Generate filename
            const formData = cvBuilder.getFormData();
            const fileName = this.generateFileName(formData);
            
            // Save the PDF
            pdf.save(fileName);
            
            // Show success message
            this.showSuccessMessage();
            
        } catch (error) {
            console.error('Error generating PDF:', error);
            this.showErrorMessage();
        } finally {
            this.isExporting = false;
            this.hideLoadingOverlay();
        }
    }
    
    generateFileName(formData) {
        const name = formData.personal?.fullName || 'CV';
        const date = new Date().toISOString().split('T')[0];
        const cleanName = name.replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '').replace(/\s+/g, '_');
        return `${cleanName}_CV_${date}.pdf`;
    }
    
    showLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.add('active');
    }
    
    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('active');
    }
    
    showSuccessMessage() {
        this.showNotification(
            languageManager.getTranslation('pdf_generated'),
            'success'
        );
    }
    
    showErrorMessage() {
        this.showNotification(
            languageManager.getTranslation('pdf_error'),
            'error'
        );
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: this.getNotificationColor(type),
            color: 'white',
            padding: '16px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: '10000',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '14px',
            fontWeight: '500',
            maxWidth: '400px',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease-in-out'
        });
        
        // Add to document
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    getNotificationColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Alternative export method using browser's print functionality
    async printToPDF() {
        try {
            this.showLoadingOverlay();
            
            // Create a new window with the CV content
            const printWindow = window.open('', '_blank');
            const cvContent = document.getElementById('cvPreview').outerHTML;
            
            // Get current theme
            const currentTheme = document.querySelector('.cv-preview').className.match(/theme-\w+/)?.[0] || 'theme-modern';
            
            printWindow.document.write(`
                <!DOCTYPE html>
                <html lang="${languageManager.getCurrentLanguage()}" dir="${languageManager.isRTL() ? 'rtl' : 'ltr'}">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>CV - ${cvBuilder.getFormData().personal?.fullName || 'Professional CV'}</title>
                    <link rel="preconnect" href="https://fonts.googleapis.com">
                    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
                    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                    <style>
                        ${this.getPrintStyles()}
                    </style>
                </head>
                <body>
                    <div class="cv-preview ${currentTheme}">
                        ${document.querySelector('.cv-content').outerHTML}
                    </div>
                </body>
                </html>
            `);
            
            printWindow.document.close();
            
            // Wait for content to load
            await this.delay(1000);
            
            // Trigger print dialog
            printWindow.print();
            
            // Close the window after printing
            setTimeout(() => {
                printWindow.close();
            }, 1000);
            
        } catch (error) {
            console.error('Error printing PDF:', error);
            this.showErrorMessage();
        } finally {
            this.hideLoadingOverlay();
        }
    }
    
    getPrintStyles() {
        // Return CSS styles optimized for printing
        return `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: ${languageManager.isRTL() ? "'Noto Sans Arabic', 'Tahoma', sans-serif" : "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif"};
                line-height: 1.6;
                color: #1e293b;
                background: white;
            }
            
            .cv-preview {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 0;
                background: white;
                box-shadow: none;
                border-radius: 0;
            }
            
            .cv-content {
                padding: 40px;
            }
            
            .cv-header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 30px;
                border-bottom: 2px solid #2563eb;
            }
            
            .cv-name {
                font-size: 36px;
                font-weight: 700;
                color: #1e293b;
                margin-bottom: 8px;
            }
            
            .cv-title {
                font-size: 20px;
                color: #2563eb;
                font-weight: 500;
                margin-bottom: 16px;
            }
            
            .cv-contact {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: 16px;
                font-size: 14px;
                color: #64748b;
            }
            
            .cv-contact-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .cv-contact-item i {
                color: #2563eb;
                width: 16px;
                text-align: center;
            }
            
            .cv-section {
                margin-bottom: 32px;
            }
            
            .cv-section-title {
                font-size: 20px;
                font-weight: 600;
                color: #1e293b;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 1px solid #cbd5e1;
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .cv-section-title i {
                color: #2563eb;
            }
            
            .cv-summary {
                font-size: 16px;
                line-height: 1.7;
                color: #475569;
                text-align: justify;
            }
            
            .cv-experience-item,
            .cv-education-item {
                margin-bottom: 24px;
                padding-bottom: 16px;
                border-bottom: 1px solid #e2e8f0;
            }
            
            .cv-experience-item:last-child,
            .cv-education-item:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            
            .cv-item-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 8px;
            }
            
            .cv-item-title {
                font-size: 18px;
                font-weight: 600;
                color: #1e293b;
            }
            
            .cv-item-company,
            .cv-item-institution {
                font-size: 16px;
                color: #2563eb;
                font-weight: 500;
            }
            
            .cv-item-date {
                font-size: 14px;
                color: #64748b;
                font-weight: 500;
                white-space: nowrap;
            }
            
            .cv-item-description {
                font-size: 14px;
                line-height: 1.6;
                color: #475569;
                margin-top: 8px;
            }
            
            .cv-skills-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
            }
            
            .cv-skill-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
            }
            
            .cv-skill-name {
                font-weight: 500;
                color: #1e293b;
            }
            
            .cv-skill-level {
                font-size: 12px;
                color: #64748b;
                background: #e2e8f0;
                padding: 4px 8px;
                border-radius: 4px;
                text-transform: uppercase;
                font-weight: 500;
            }
            
            .cv-languages-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 12px;
            }
            
            .cv-language-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px;
                background: #f8fafc;
                border-radius: 6px;
            }
            
            .cv-language-name {
                font-weight: 500;
                color: #1e293b;
            }
            
            .cv-language-level {
                font-size: 12px;
                color: #2563eb;
                font-weight: 600;
            }
            
            /* Theme-specific print styles */
            .cv-preview.theme-modern .cv-header {
                background: #2563eb !important;
                color: white !important;
                padding: 40px;
                margin: -40px -40px 40px;
                border-radius: 0;
                border-bottom: none;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .cv-preview.theme-modern .cv-name {
                color: white !important;
            }
            
            .cv-preview.theme-modern .cv-title {
                color: rgba(255, 255, 255, 0.9) !important;
            }
            
            .cv-preview.theme-modern .cv-contact {
                color: rgba(255, 255, 255, 0.8) !important;
            }
            
            .cv-preview.theme-modern .cv-contact-item i {
                color: white !important;
            }
            
            @page {
                margin: 0;
                size: A4;
            }
            
            @media print {
                body {
                    -webkit-print-color-adjust: exact;
                    color-adjust: exact;
                }
            }
        `;
    }
}

// Initialize PDF Exporter
const pdfExporter = new PDFExporter();

// Export for global access
window.pdfExporter = pdfExporter;
