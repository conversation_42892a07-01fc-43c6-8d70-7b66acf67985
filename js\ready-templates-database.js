// ELASHRAFY CV - Ready Templates Database
// قاعدة بيانات القوالب الجاهزة - الأشرافي للسيرة الذاتية

class ReadyTemplatesDatabase {
    constructor() {
        this.templates = [];
        this.categories = {
            modern: 'عصري',
            creative: 'إبداعي', 
            executive: 'تنفيذي',
            academic: 'أكاديمي',
            technical: 'تقني',
            medical: 'طبي',
            business: 'تجاري',
            legal: 'قانوني',
            artistic: 'فني',
            minimalist: 'مينيمال',
            luxury: 'فاخر',
            corporate: 'مؤسسي',
            startup: 'ناشئ',
            freelancer: 'مستقل',
            designer: 'مصمم'
        };
        
        this.styles = {
            'glass-morphism': 'زجاجي شفاف',
            'neo-brutalism': 'جريء عصري',
            'gradient-mesh': 'تدرج شبكي',
            'organic-shapes': 'أشكال عضوية',
            'geometric-art': 'فن هندسي',
            'watercolor': 'ألوان مائية',
            'paper-cut': 'قطع ورقي',
            'neon-cyber': 'نيون سايبر',
            'vintage-retro': 'كلاسيكي عتيق',
            'crystal-clear': 'كريستال صافي',
            'aurora-borealis': 'شفق قطبي',
            'holographic': 'هولوجرافي'
        };
        
        this.init();
    }
    
    init() {
        this.generateProfessionalTemplates();
        console.log(`✨ تم إنشاء ${this.templates.length} قالب احترافي جاهز`);
    }
    
    generateProfessionalTemplates() {
        // Generate 400+ professional templates
        let templateId = 1;
        
        Object.keys(this.categories).forEach(category => {
            Object.keys(this.styles).forEach(style => {
                // Generate 2-3 templates per category-style combination
                for (let variant = 1; variant <= 3; variant++) {
                    const template = this.createProfessionalTemplate(
                        templateId++,
                        category,
                        style,
                        variant
                    );
                    this.templates.push(template);
                }
            });
        });
        
        // Add some extra premium templates
        this.addPremiumTemplates(templateId);
    }
    
    createProfessionalTemplate(id, category, style, variant) {
        const colors = this.getColorPalette(category, style);
        const layout = this.getLayoutStructure(category, style);
        
        return {
            id: `template_${id}`,
            name: this.generateTemplateName(category, style, variant),
            category: category,
            style: style,
            variant: variant,
            description: this.generateTemplateDescription(category, style),
            preview: this.generateTemplatePreview(category, style, colors, layout),
            thumbnail: this.generateTemplateThumbnail(category, style, colors),
            isPremium: this.isPremiumTemplate(category, style),
            rating: this.generateRating(),
            downloads: this.generateDownloads(),
            tags: this.generateTemplateTags(category, style),
            colors: colors,
            layout: layout,
            fonts: this.getRecommendedFonts(category, style),
            features: this.getTemplateFeatures(category, style),
            atsCompatible: true,
            multiPage: Math.random() > 0.4,
            photoSupport: true,
            customizable: true,
            responsive: true,
            printOptimized: true,
            languages: ['ar', 'en'],
            industries: this.getTargetIndustries(category),
            experience: this.getTargetExperience(category),
            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString(),
            author: 'محمد الأشرافي',
            version: '1.0.0'
        };
    }
    
    generateTemplateName(category, style, variant) {
        const categoryName = this.categories[category];
        const styleName = this.styles[style];
        
        const nameVariants = {
            1: 'كلاسيك',
            2: 'برو',
            3: 'إليت'
        };
        
        return `${categoryName} ${styleName} ${nameVariants[variant]}`;
    }
    
    generateTemplateDescription(category, style) {
        const descriptions = {
            modern: {
                'glass-morphism': 'تصميم عصري بتأثيرات زجاجية شفافة وخطوط نظيفة',
                'neo-brutalism': 'تصميم جريء وقوي مع ألوان صارخة وخطوط واضحة',
                'gradient-mesh': 'تدرجات لونية متطورة تخلق عمقاً بصرياً رائعاً'
            },
            creative: {
                'organic-shapes': 'أشكال طبيعية متدفقة تضفي حيوية وإبداعاً على التصميم',
                'watercolor': 'تأثيرات الألوان المائية الناعمة والفنية الجذابة',
                'geometric-art': 'فن هندسي معاصر مع أشكال هندسية متوازنة ومبتكرة'
            },
            executive: {
                'luxury': 'تصميم فاخر وأنيق يعكس الاحترافية والخبرة القيادية',
                'crystal-clear': 'وضوح كريستالي مع انعكاسات ضوئية راقية',
                'holographic': 'تأثيرات هولوجرافية متقدمة تضفي طابعاً مستقبلياً'
            }
        };
        
        const categoryDescriptions = descriptions[category];
        if (categoryDescriptions && categoryDescriptions[style]) {
            return categoryDescriptions[style];
        }
        
        return `تصميم ${this.categories[category]} بنمط ${this.styles[style]} احترافي ومتميز`;
    }
    
    generateTemplatePreview(category, style, colors, layout) {
        // Generate SVG preview based on category and style
        const { primary, secondary, accent, background } = colors;
        
        const svgContent = this.createAdvancedSVGPreview(style, colors, layout);
        return `data:image/svg+xml;base64,${btoa(svgContent)}`;
    }
    
    createAdvancedSVGPreview(style, colors, layout) {
        const { primary, secondary, accent, background } = colors;
        
        const templates = {
            'glass-morphism': this.createGlassMorphismSVG(colors),
            'neo-brutalism': this.createNeoBrutalismSVG(colors),
            'gradient-mesh': this.createGradientMeshSVG(colors),
            'organic-shapes': this.createOrganicShapesSVG(colors),
            'geometric-art': this.createGeometricArtSVG(colors),
            'watercolor': this.createWatercolorSVG(colors),
            'neon-cyber': this.createNeonCyberSVG(colors),
            'crystal-clear': this.createCrystalClearSVG(colors),
            'luxury': this.createLuxurySVG(colors),
            'holographic': this.createHolographicSVG(colors)
        };
        
        return templates[style] || templates['glass-morphism'];
    }
    
    createGlassMorphismSVG(colors) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="glassGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.8" />
                    <stop offset="50%" style="stop-color:${accent};stop-opacity:0.6" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.4" />
                </linearGradient>
                <filter id="blur" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="4"/>
                </filter>
                <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
                    <feMerge> 
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
            
            <!-- Background -->
            <rect width="300" height="400" fill="url(#glassGrad)" rx="16"/>
            
            <!-- Glass morphism shapes -->
            <circle cx="80" cy="80" r="60" fill="rgba(255,255,255,0.2)" filter="url(#blur)"/>
            <circle cx="220" cy="320" r="50" fill="rgba(255,255,255,0.15)" filter="url(#blur)"/>
            
            <!-- Header section -->
            <rect x="30" y="30" width="240" height="80" fill="rgba(255,255,255,0.25)" 
                  rx="20" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
            
            <!-- Photo -->
            <circle cx="80" cy="70" r="25" fill="rgba(255,255,255,0.9)" filter="url(#glow)"/>
            <circle cx="80" cy="70" r="20" fill="${accent}" opacity="0.7"/>
            
            <!-- Name and title -->
            <rect x="120" y="50" width="140" height="12" fill="rgba(255,255,255,0.9)" rx="6"/>
            <rect x="120" y="68" width="100" height="8" fill="rgba(255,255,255,0.8)" rx="4"/>
            <rect x="120" y="82" width="120" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>
            
            <!-- Content sections -->
            <rect x="30" y="130" width="240" height="60" fill="rgba(255,255,255,0.2)" 
                  rx="15" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="45" y="145" width="80" height="8" fill="rgba(255,255,255,0.8)" rx="4"/>
            <rect x="45" y="160" width="200" height="4" fill="rgba(255,255,255,0.6)" rx="2"/>
            <rect x="45" y="168" width="180" height="4" fill="rgba(255,255,255,0.5)" rx="2"/>
            <rect x="45" y="176" width="190" height="4" fill="rgba(255,255,255,0.4)" rx="2"/>
            
            <!-- Experience section -->
            <rect x="30" y="210" width="240" height="80" fill="rgba(255,255,255,0.18)" 
                  rx="15" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="45" y="225" width="90" height="8" fill="rgba(255,255,255,0.8)" rx="4"/>
            <rect x="45" y="240" width="200" height="4" fill="rgba(255,255,255,0.6)" rx="2"/>
            <rect x="45" y="248" width="180" height="4" fill="rgba(255,255,255,0.5)" rx="2"/>
            <rect x="45" y="256" width="190" height="4" fill="rgba(255,255,255,0.4)" rx="2"/>
            <rect x="45" y="270" width="170" height="4" fill="rgba(255,255,255,0.4)" rx="2"/>
            
            <!-- Skills section -->
            <rect x="30" y="310" width="240" height="50" fill="rgba(255,255,255,0.15)" 
                  rx="12" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="45" y="325" width="60" height="6" fill="rgba(255,255,255,0.8)" rx="3"/>
            <rect x="115" y="325" width="55" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>
            <rect x="180" y="325" width="50" height="6" fill="rgba(255,255,255,0.6)" rx="3"/>
            <rect x="45" y="340" width="65" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>
            <rect x="120" y="340" width="45" height="6" fill="rgba(255,255,255,0.6)" rx="3"/>
            
            <!-- Decorative elements -->
            <circle cx="260" cy="50" r="12" fill="rgba(255,255,255,0.3)" filter="url(#blur)"/>
            <circle cx="50" cy="380" r="10" fill="rgba(255,255,255,0.25)" filter="url(#blur)"/>
        </svg>`;
    }
    
    createNeoBrutalismSVG(colors) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="6" dy="6" stdDeviation="0" flood-color="#000" flood-opacity="1"/>
                </filter>
            </defs>
            
            <!-- Bold background -->
            <rect width="300" height="400" fill="${primary}" stroke="#000" stroke-width="4"/>
            
            <!-- Header -->
            <rect x="20" y="20" width="260" height="80" fill="${accent}" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            
            <!-- Photo -->
            <rect x="40" y="35" width="50" height="50" fill="#fff" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="45" y="40" width="40" height="40" fill="${secondary}"/>
            
            <!-- Name and title -->
            <rect x="110" y="40" width="140" height="12" fill="#000"/>
            <rect x="110" y="58" width="100" height="8" fill="#000"/>
            <rect x="110" y="72" width="120" height="6" fill="#000"/>
            
            <!-- Content blocks -->
            <rect x="25" y="120" width="250" height="50" fill="${secondary}" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="40" y="135" width="80" height="8" fill="#000"/>
            <rect x="40" y="150" width="200" height="4" fill="#000"/>
            <rect x="40" y="158" width="180" height="4" fill="#000"/>
            
            <!-- Experience block -->
            <rect x="25" y="190" width="250" height="70" fill="#fff" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="40" y="205" width="90" height="8" fill="#000"/>
            <rect x="40" y="220" width="200" height="4" fill="#000"/>
            <rect x="40" y="228" width="180" height="4" fill="#000"/>
            <rect x="40" y="236" width="190" height="4" fill="#000"/>
            <rect x="40" y="244" width="170" height="4" fill="#000"/>
            
            <!-- Skills blocks -->
            <rect x="25" y="280" width="80" height="30" fill="${accent}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="115" y="280" width="75" height="30" fill="${primary}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="200" y="280" width="75" height="30" fill="${secondary}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            
            <!-- Footer -->
            <rect x="25" y="330" width="250" height="40" fill="#fff" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="40" y="345" width="60" height="6" fill="#000"/>
            <rect x="110" y="345" width="50" height="6" fill="#000"/>
            <rect x="170" y="345" width="70" height="6" fill="#000"/>
        </svg>`;
    }
    
    getColorPalette(category, style) {
        const palettes = {
            modern: {
                primary: '#1e40af',
                secondary: '#3b82f6', 
                accent: '#8b5cf6',
                background: '#f8fafc',
                text: '#1e293b'
            },
            creative: {
                primary: '#7c2d12',
                secondary: '#ea580c',
                accent: '#fb923c', 
                background: '#fff7ed',
                text: '#9a3412'
            },
            executive: {
                primary: '#0f172a',
                secondary: '#1e293b',
                accent: '#334155',
                background: '#f1f5f9',
                text: '#020617'
            },
            luxury: {
                primary: '#7f1d1d',
                secondary: '#dc2626',
                accent: '#f87171',
                background: '#fef2f2', 
                text: '#450a0a'
            }
        };
        
        return palettes[category] || palettes.modern;
    }
    
    getLayoutStructure(category, style) {
        const layouts = {
            modern: 'single-column-header',
            creative: 'asymmetric-creative',
            executive: 'traditional-formal',
            academic: 'detailed-sections',
            technical: 'skills-focused',
            medical: 'clean-professional'
        };
        
        return layouts[category] || 'single-column-header';
    }
    
    isPremiumTemplate(category, style) {
        const premiumStyles = ['luxury', 'holographic', 'crystal-clear', 'aurora-borealis'];
        const premiumCategories = ['executive', 'luxury'];
        
        return premiumStyles.includes(style) || premiumCategories.includes(category) || Math.random() > 0.7;
    }
    
    generateRating() {
        return (4.0 + Math.random() * 1.0).toFixed(1);
    }
    
    generateDownloads() {
        return Math.floor(Math.random() * 10000) + 500;
    }
    
    generateTemplateTags(category, style) {
        const baseTags = ['احترافي', 'جميل', 'حديث', 'متميز'];
        const categoryTags = {
            modern: ['عصري', 'نظيف', 'أنيق'],
            creative: ['إبداعي', 'فني', 'ملون'],
            executive: ['تنفيذي', 'قيادي', 'فاخر'],
            academic: ['أكاديمي', 'علمي', 'بحثي'],
            technical: ['تقني', 'هندسي', 'متطور']
        };
        
        const styleTags = {
            'glass-morphism': ['شفاف', 'زجاجي'],
            'neo-brutalism': ['جريء', 'قوي'],
            'gradient-mesh': ['متدرج', 'ملون'],
            'organic-shapes': ['طبيعي', 'متدفق'],
            'geometric-art': ['هندسي', 'متوازن']
        };
        
        return [
            ...baseTags,
            ...(categoryTags[category] || []),
            ...(styleTags[style] || [])
        ];
    }
    
    getRecommendedFonts(category, style) {
        const fonts = {
            modern: ['Cairo', 'Inter', 'Roboto'],
            creative: ['Amiri', 'Playfair Display', 'Montserrat'],
            executive: ['Times New Roman', 'Georgia', 'Crimson Text'],
            academic: ['Noto Sans Arabic', 'Source Sans Pro', 'Lato'],
            technical: ['Fira Code', 'Source Code Pro', 'Ubuntu Mono']
        };
        
        return fonts[category] || fonts.modern;
    }
    
    getTemplateFeatures(category, style) {
        const baseFeatures = ['ATS متوافق', 'متعدد الصفحات', 'دعم الصور', 'قابل للتخصيص'];
        const styleFeatures = {
            'glass-morphism': ['تأثيرات زجاجية', 'شفافية متقدمة'],
            'neo-brutalism': ['تصميم جريء', 'ألوان قوية'],
            'gradient-mesh': ['تدرجات معقدة', 'عمق بصري'],
            'luxury': ['تصميم فاخر', 'تفاصيل راقية']
        };
        
        return [...baseFeatures, ...(styleFeatures[style] || [])];
    }
    
    getTargetIndustries(category) {
        const industries = {
            modern: ['تكنولوجيا', 'تسويق', 'تصميم'],
            creative: ['فنون', 'إعلام', 'تصميم جرافيك'],
            executive: ['إدارة', 'استشارات', 'مالية'],
            academic: ['تعليم', 'بحث علمي', 'جامعات'],
            technical: ['هندسة', 'برمجة', 'تكنولوجيا'],
            medical: ['طب', 'صحة', 'صيدلة'],
            business: ['أعمال', 'مبيعات', 'تجارة'],
            legal: ['قانون', 'محاماة', 'قضاء']
        };
        
        return industries[category] || ['عام'];
    }
    
    getTargetExperience(category) {
        const experience = {
            academic: ['طالب', 'خريج جديد', 'باحث'],
            executive: ['مدير', 'مدير تنفيذي', 'قائد فريق'],
            technical: ['مطور', 'مهندس', 'محلل تقني']
        };
        
        return experience[category] || ['جميع المستويات'];
    }
    
    addPremiumTemplates(startId) {
        // Add 50 extra premium templates
        for (let i = 0; i < 50; i++) {
            const premiumTemplate = this.createPremiumTemplate(startId + i);
            this.templates.push(premiumTemplate);
        }
    }
    
    createPremiumTemplate(id) {
        const premiumCategories = ['luxury', 'executive', 'creative'];
        const premiumStyles = ['holographic', 'crystal-clear', 'aurora-borealis'];
        
        const category = premiumCategories[Math.floor(Math.random() * premiumCategories.length)];
        const style = premiumStyles[Math.floor(Math.random() * premiumStyles.length)];
        
        const template = this.createProfessionalTemplate(id, category, style, 1);
        template.isPremium = true;
        template.name = `${template.name} بريميوم`;
        template.rating = (4.5 + Math.random() * 0.5).toFixed(1);
        template.downloads = Math.floor(Math.random() * 5000) + 2000;
        
        return template;
    }
    
    // Search and filter methods
    searchTemplates(query) {
        if (!query) return this.templates;
        
        const searchTerm = query.toLowerCase();
        return this.templates.filter(template => 
            template.name.toLowerCase().includes(searchTerm) ||
            template.description.toLowerCase().includes(searchTerm) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            this.categories[template.category].toLowerCase().includes(searchTerm) ||
            this.styles[template.style].toLowerCase().includes(searchTerm)
        );
    }
    
    filterByCategory(category) {
        if (category === 'all') return this.templates;
        return this.templates.filter(template => template.category === category);
    }
    
    filterByStyle(style) {
        if (style === 'all') return this.templates;
        return this.templates.filter(template => template.style === style);
    }
    
    sortTemplates(templates, sortBy) {
        switch (sortBy) {
            case 'popular':
                return templates.sort((a, b) => b.downloads - a.downloads);
            case 'newest':
                return templates.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            case 'rating':
                return templates.sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
            case 'name':
                return templates.sort((a, b) => a.name.localeCompare(b.name));
            default:
                return templates;
        }
    }
    
    getTemplateById(id) {
        return this.templates.find(template => template.id === id);
    }
    
    getTemplatesByCategory(category) {
        return this.templates.filter(template => template.category === category);
    }
    
    getFeaturedTemplates(count = 12) {
        return this.templates
            .filter(template => parseFloat(template.rating) >= 4.5)
            .sort((a, b) => b.downloads - a.downloads)
            .slice(0, count);
    }
    
    getPopularTemplates(count = 20) {
        return this.templates
            .sort((a, b) => b.downloads - a.downloads)
            .slice(0, count);
    }
    
    getNewestTemplates(count = 15) {
        return this.templates
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, count);
    }
    
    getPremiumTemplates() {
        return this.templates.filter(template => template.isPremium);
    }
    
    getFreeTemplates() {
        return this.templates.filter(template => !template.isPremium);
    }
    
    getTemplateStats() {
        return {
            total: this.templates.length,
            premium: this.getPremiumTemplates().length,
            free: this.getFreeTemplates().length,
            categories: Object.keys(this.categories).length,
            styles: Object.keys(this.styles).length,
            averageRating: (this.templates.reduce((sum, t) => sum + parseFloat(t.rating), 0) / this.templates.length).toFixed(1),
            totalDownloads: this.templates.reduce((sum, t) => sum + t.downloads, 0)
        };
    }
}

// Initialize the templates database
const readyTemplatesDB = new ReadyTemplatesDatabase();

// Export for global access
window.readyTemplatesDB = readyTemplatesDB;
