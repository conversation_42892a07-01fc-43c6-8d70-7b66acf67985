<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional CV Builder | منشئ السيرة الذاتية المهنية</title>

    <!-- Meta Tags -->
    <meta name="description" content="Create professional CVs with bilingual support (Arabic/English). Modern, responsive design with PDF export functionality.">
    <meta name="keywords" content="CV builder, resume maker, Arabic CV, English CV, PDF export, professional resume">
    <meta name="author" content="CV Builder Team">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://cv-builder.app/">
    <meta property="og:title" content="Professional CV Builder">
    <meta property="og:description" content="Create professional CVs with bilingual support (Arabic/English). Modern, responsive design with PDF export functionality.">
    <meta property="og:image" content="https://cv-builder.app/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://cv-builder.app/">
    <meta property="twitter:title" content="Professional CV Builder">
    <meta property="twitter:description" content="Create professional CVs with bilingual support (Arabic/English). Modern, responsive design with PDF export functionality.">
    <meta property="twitter:image" content="https://cv-builder.app/twitter-image.png">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Theme Color -->
    <meta name="theme-color" content="#2563eb">
    <meta name="msapplication-TileColor" content="#2563eb">

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDE4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iMTgwIiByeD0iMjAiIGZpbGw9IiMyNTYzZWIiLz4KPHN2ZyB4PSI0NSIgeT0iNDUiIHdpZHRoPSI5MCIgaGVpZ2h0PSI5MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Ik0xNCAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWOHoiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPC9zdmc+Cjwvc3ZnPgo=">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzI1NjNlYiIvPgo8c3ZnIHg9IjgiIHk9IjgiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Ik0xNCAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWOHoiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPC9zdmc+Cjwvc3ZnPgo=">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Header -->
    <header class="app-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-file-alt"></i>
                    <span class="logo-text" data-translate="app_title">CV Builder</span>
                </div>
                
                <div class="header-controls">
                    <!-- Language Toggle -->
                    <div class="language-toggle">
                        <button class="lang-btn active" data-lang="en">EN</button>
                        <button class="lang-btn" data-lang="ar">عر</button>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <select id="themeSelect">
                            <option value="modern" data-translate="theme_modern">Modern</option>
                            <option value="classic" data-translate="theme_classic">Classic</option>
                            <option value="creative" data-translate="theme_creative">Creative</option>
                            <option value="minimal" data-translate="theme_minimal">Minimal</option>
                        </select>
                    </div>
                    
                    <!-- Demo Button -->
                    <button class="demo-btn" id="loadDemo">
                        <i class="fas fa-magic"></i>
                        <span data-translate="load_demo">Load Demo</span>
                    </button>

                    <!-- Export Button -->
                    <button class="export-btn" id="exportPDF">
                        <i class="fas fa-download"></i>
                        <span data-translate="export_pdf">Export PDF</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Application -->
    <main class="app-main">
        <div class="container">
            <div class="app-layout">
                <!-- CV Form Panel -->
                <aside class="form-panel">
                    <div class="form-container">
                        <h2 data-translate="build_cv">Build Your CV</h2>
                        
                        <!-- Progress Indicator -->
                        <div class="progress-indicator">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <span class="progress-text" id="progressText">0% Complete</span>
                        </div>

                        <!-- Form Sections -->
                        <form id="cvForm" class="cv-form">
                            <!-- Personal Information -->
                            <section class="form-section active" data-section="personal">
                                <h3>
                                    <i class="fas fa-user"></i>
                                    <span data-translate="personal_info">Personal Information</span>
                                </h3>
                                
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="fullName" data-translate="full_name">Full Name</label>
                                        <input type="text" id="fullName" name="fullName" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="jobTitle" data-translate="job_title">Job Title</label>
                                        <input type="text" id="jobTitle" name="jobTitle" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="email" data-translate="email">Email</label>
                                        <input type="email" id="email" name="email" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="phone" data-translate="phone">Phone</label>
                                        <input type="tel" id="phone" name="phone" required>
                                    </div>
                                    
                                    <div class="form-group full-width">
                                        <label for="address" data-translate="address">Address</label>
                                        <input type="text" id="address" name="address">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="linkedin" data-translate="linkedin">LinkedIn</label>
                                        <input type="url" id="linkedin" name="linkedin">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="website" data-translate="website">Website</label>
                                        <input type="url" id="website" name="website">
                                    </div>
                                    
                                    <div class="form-group full-width">
                                        <label for="summary" data-translate="professional_summary">Professional Summary</label>
                                        <textarea id="summary" name="summary" rows="4" placeholder="Brief description of your professional background..."></textarea>
                                    </div>
                                </div>
                            </section>

                            <!-- Experience Section -->
                            <section class="form-section" data-section="experience">
                                <h3>
                                    <i class="fas fa-briefcase"></i>
                                    <span data-translate="work_experience">Work Experience</span>
                                </h3>
                                
                                <div id="experienceContainer" class="dynamic-container">
                                    <!-- Experience items will be added dynamically -->
                                </div>
                                
                                <button type="button" class="add-btn" id="addExperience">
                                    <i class="fas fa-plus"></i>
                                    <span data-translate="add_experience">Add Experience</span>
                                </button>
                            </section>

                            <!-- Education Section -->
                            <section class="form-section" data-section="education">
                                <h3>
                                    <i class="fas fa-graduation-cap"></i>
                                    <span data-translate="education">Education</span>
                                </h3>
                                
                                <div id="educationContainer" class="dynamic-container">
                                    <!-- Education items will be added dynamically -->
                                </div>
                                
                                <button type="button" class="add-btn" id="addEducation">
                                    <i class="fas fa-plus"></i>
                                    <span data-translate="add_education">Add Education</span>
                                </button>
                            </section>

                            <!-- Skills Section -->
                            <section class="form-section" data-section="skills">
                                <h3>
                                    <i class="fas fa-cogs"></i>
                                    <span data-translate="skills">Skills</span>
                                </h3>
                                
                                <div id="skillsContainer" class="skills-container">
                                    <!-- Skills will be added dynamically -->
                                </div>
                                
                                <div class="skill-input-group">
                                    <input type="text" id="skillInput" placeholder="Enter a skill...">
                                    <select id="skillLevel">
                                        <option value="beginner" data-translate="beginner">Beginner</option>
                                        <option value="intermediate" data-translate="intermediate">Intermediate</option>
                                        <option value="advanced" data-translate="advanced">Advanced</option>
                                        <option value="expert" data-translate="expert">Expert</option>
                                    </select>
                                    <button type="button" id="addSkill">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </section>

                            <!-- Languages Section -->
                            <section class="form-section" data-section="languages">
                                <h3>
                                    <i class="fas fa-language"></i>
                                    <span data-translate="languages">Languages</span>
                                </h3>
                                
                                <div id="languagesContainer" class="dynamic-container">
                                    <!-- Languages will be added dynamically -->
                                </div>
                                
                                <button type="button" class="add-btn" id="addLanguage">
                                    <i class="fas fa-plus"></i>
                                    <span data-translate="add_language">Add Language</span>
                                </button>
                            </section>
                        </form>

                        <!-- Navigation Buttons -->
                        <div class="form-navigation">
                            <button type="button" class="nav-btn prev-btn" id="prevSection" disabled>
                                <i class="fas fa-chevron-left"></i>
                                <span data-translate="previous">Previous</span>
                            </button>
                            <button type="button" class="nav-btn next-btn" id="nextSection">
                                <span data-translate="next">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </aside>

                <!-- CV Preview Panel -->
                <main class="preview-panel">
                    <div class="preview-container">
                        <div class="preview-header">
                            <h2 data-translate="preview">Preview</h2>
                            <div class="preview-controls">
                                <button class="zoom-btn" id="zoomOut">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <span class="zoom-level" id="zoomLevel">100%</span>
                                <button class="zoom-btn" id="zoomIn">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="preview-wrapper" id="previewWrapper">
                            <div class="cv-preview" id="cvPreview">
                                <!-- CV content will be generated here -->
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p data-translate="generating_pdf">Generating PDF...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/cv-builder.js"></script>
    <script src="js/pdf-export.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
